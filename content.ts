import { Base64 } from "js-base64";
var defaultExcludeDomains = ["ogs.google.com", "meet.google.com", "play.google.com", "docs.google.com", "keep.google.com", "chrome.google.com", "support.mozilla.org",
    "codefileclient.danid.dk", "applet.danid.dk", "nemlog-in.mitid.dk", "business.comcast.com" ];
var openrpadebug = false;
var openrpauniquexpathids = ['ng-model', 'ng-reflect-name']; // aria-label
var openrpautil = null;
if (openrpadebug) console.log("Hi from content");

function inIframe() {
    var result = true;
    try {
        if (window.self === window.top) return false;
        if (parent) {
        }

    } catch (e) {
    }
    return result;
}

function remotePushEvent(evt) {
    // if (openrpadebug) console.log("remotePushEvent, message received", evt);
    if (evt.data != null && evt.data.functionName == "mousemove") {
        var message = evt.data;
        if (typeof message === "string") {
            try {
                message = JSON.parse(message);
            } catch (e) { }
        }
        if (evt.data != null && evt.data.functionName == "mousemove") {
            openrpautil.parent = evt.data;
            try {
                notifyFrames(null);
            } catch (e) { }
        }
    }
}
if (window.addEventListener) {
    window.addEventListener("message", remotePushEvent, false);
} else {
    (window as any).attachEvent("onmessage", remotePushEvent);
}
var disallowPost = function() {
    if (document.URL.startsWith("https://docs.google.com/spreadsheets/d")) {
        return true;
    }
    if (document.URL.startsWith("https://codefileclient.danid.dk")) {
        return true;
    }
    if (document.URL.startsWith("https://applet.danid.dk")) {
        return true;
    }
    if (document.URL.startsWith("https://nemlog-in.mitid.dk")) {
        return true;
    }
    console.log(document.URL);
    return false
}
var notifyFrames = (event) => {
    if(disallowPost()) return;
    for (let targetElement of (document.getElementsByTagName('iframe') as any)) {
        var message = { functionName: 'mousemove', parents: 0, xpaths: [], uix: "", uiy: "", cssPath: "", xPath: "" };
        try {
            openrpautil.applyPhysicalCords(message, targetElement);
        } catch (e) {
            console.error(e);
        }
        if (openrpautil.parent != null) {
            message.parents = openrpautil.parent.parents + 1;
            message.uix += openrpautil.parent.uix;
            message.uiy += openrpautil.parent.uiy;
        }
        var width: string | number = getComputedStyle(targetElement, null).getPropertyValue('border-width');
        width = parseInt(width.replace('px', '')) * 0.85;
        message.uix += (width | 0);
        var height: string | number = getComputedStyle(targetElement, null).getPropertyValue('border-height');
        height = parseInt(height.replace('px', '')) * 0.85;
        message.uiy += (height | 0);

        message.cssPath = UTILS.cssPath(targetElement, false);
        message.xPath = UTILS.xPath(targetElement, true);
        // if (openrpadebug) console.log("post message to frame", message);
        targetElement.contentWindow.postMessage(JSON.stringify(message), '*');
    }
    var doFrames = () => {
        try {
            for (let targetElement of (document.getElementsByTagName('frame') as any)) {
                var message = { functionName: 'mousemove', parents: 0, xpaths: [], uix: "", uiy: "", cssPath: "", xPath: "" };
                try {
                    openrpautil.applyPhysicalCords(message, targetElement);
                } catch (e) {
                    console.error(e);
                }
                if (openrpautil.parent != null) {
                    message.parents = openrpautil.parent.parents + 1;
                    message.uix += openrpautil.parent.uix;
                    message.uiy += openrpautil.parent.uiy;
                }
                var width: number | string = getComputedStyle(targetElement, null).getPropertyValue('border-width');
                width = parseInt(width.replace('px', '')) * 0.85;
                message.uix += width;
                var height: number | string = getComputedStyle(targetElement, null).getPropertyValue('border-height');
                height = parseInt(height.replace('px', '')) * 0.85;
                message.uiy += (height | 0);

                message.cssPath = UTILS.cssPath(targetElement, false);
                message.xPath = UTILS.xPath(targetElement, true);
                targetElement.contentDocument.openrpautil.parent = message;
            }
        } catch (e) {
            setTimeout(doFrames, 500);
        }
    };
    doFrames();
}
if (!document.URL.startsWith("https://docs.google.com/spreadsheets/d")) {
    window.addEventListener('load', notifyFrames);
} else {
    if (openrpadebug) console.warn("skip google docs");
}


var runtimeOnMessage = function (sender, message, fnResponse) {
    try {
        if ((sender && sender.openrpadebug) || openrpadebug) {
            if (openrpadebug) console.debug("sender", sender);
            if (openrpadebug) console.debug("message", message);
            if (openrpadebug) console.debug("fnResponse", fnResponse);
        }
        if (openrpautil == undefined) return;
        var func = openrpautil[sender.functionName];
        if (func) {
            var result = func(sender);
            if (result == null) {
                if (openrpadebug) console.warn(sender.functionName + " gave no result.");
                fnResponse(sender);
            } else {
                fnResponse(result);
            }
        }
        else {
            sender.error = "Unknown function " + sender.functionName;
            fnResponse(sender);
        }
    } catch (e) {
        console.error('chrome.runtime.onMessage: error ');
        console.error(e);
        sender.error = e;
        fnResponse(sender);
    }
}

function doit() {
    try {
        // @ts-ignore
        chrome.extension.onMessage.addListener(runtimeOnMessage);
    } catch (error) {
    }
    try {
        chrome.runtime.onMessage.addListener(runtimeOnMessage);
    } catch (error) {
    }

    if (openrpadebug) console.debug('declaring openrpautil class 1');
    var cachecount = 0;
    openrpautil = {
        parent: null,
        ping: function () {
            return "pong";
        },
        init: function () {
            if (document.URL.startsWith("https://docs.google.com/spreadsheets/d")) {
                if (openrpadebug) console.warn("skip google docs");
                return;
            }
            document.addEventListener('mousemove', function (e) { openrpautil.pushEvent('mousemove', e); }, true);
            if (inIframe()) {
                // if (openrpadebug) console.debug('in iframe, only register mouse move listener');
                return;
            }
            document.addEventListener('click', function (e) { openrpautil.pushEvent('click', e); }, true);
            document.addEventListener('keydown', function (e) { openrpautil.pushEvent('keydown', e); }, true);
            document.addEventListener('keypress', function (e) { openrpautil.pushEvent('keyup', e); }, true);
            document.addEventListener('mousedown', function (e) { openrpautil.pushEvent('mousedown', e); }, true);
        },
        findform: function (element) {
            try {
                var form = null;
                var ele = element;
                while (ele && !form) {
                    var name = ele.localName;
                    if (!name) {
                        ele = ele.parentNode;
                        continue;
                    }
                    name = name.toLowerCase();
                    if (name === "form") form = ele;
                    ele = ele.parentNode;
                }
                return form;
            } catch (e) {
                console.error(e);
                return null;
            }
        },
        clickelement: function (message) {
            openrpadebug = message.debug;
            if (message.uniquexpathids) openrpauniquexpathids = message.uniquexpathids;
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
            }
            if (message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (xpathEle === null) message.xPath = 'false';
                if (xpathEle !== null) message.xPath = 'true';
                ele = xpathEle;
            }
            if (message.cssPath && ele === null) {
                var cssEle = document.querySelector(message.cssPath);
                if (cssEle === null) message.cssPath = 'false';
                if (cssEle !== null) message.cssPath = 'true';
                ele = cssEle;
            }
            try {
                if (ele !== null && ele !== undefined) {
                    var tagname = ele.tagName;
                    var tagtype = ele.getAttribute("type");
                    if (tagname) tagname = tagname.toLowerCase();
                    if (tagtype) tagtype = tagtype.toLowerCase();
                    if (tagname == "input" || tagtype == "type") {
                        var events = ["mousedown", "mouseup", "click", "submit"];
                        for (var i = 0; i < events.length; ++i) {
                            simulate(ele, events[i]);
                        }
                    } else {
                        var events = ["mousedown", "mouseup", "click"];
                        for (var i = 0; i < events.length; ++i) {
                            simulate(ele, events[i]);
                        }
                    }
                }
            } catch (e) {
                console.error(e);
                message.error = e.message;
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug) console.log(test);
            return test;
        },
        focuselement: function (message) {
            openrpadebug = message.debug;
            if (message.uniquexpathids) openrpauniquexpathids = message.uniquexpathids;
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
            }
            if (message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (xpathEle === null) message.xPath = 'false';
                if (xpathEle !== null) message.xPath = 'true';
                ele = xpathEle;
            }
            if (message.cssPath && ele === null) {
                var cssEle = document.querySelector(message.cssPath);
                if (cssEle === null) message.cssPath = 'false';
                if (cssEle !== null) message.cssPath = 'true';
                ele = cssEle;
            }
            try {
                if (ele !== null && ele !== undefined) {
                    ele.scrollIntoView({ block: "center", behaviour: "smooth" });
                    var eventType = "onfocusin" in ele ? "focusin" : "focus",
                        bubbles = "onfocusin" in ele,
                        event;

                    if ("createEvent" in document) {
                        event = document.createEvent("Event");
                        event.initEvent(eventType, bubbles, true);
                    }
                    else if ("Event" in window) {
                        event = new Event(eventType, { bubbles: bubbles, cancelable: true });
                    }

                    ele.focus();
                    ele.dispatchEvent(event);
                    // openrpautil.getelement(message);
                }
            } catch (e) {
                console.error(e);
                message.error = e;
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug) console.log(test);
            return test;
        },
        getelements: function (message) {
            try {
                openrpadebug = message.debug;
                if (message.uniquexpathids) openrpauniquexpathids = message.uniquexpathids;
                var fromele = null;
                if (message.fromxPath != null && message.fromxPath != "") {
                    if (message.fromxPath != null && message.fromxPath != "") {
                        if (openrpadebug) console.log("fromele = document.evaluate('" + message.fromxPath + "', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;");
                        fromele = document.evaluate(message.fromxPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    } else if (message.fromcssPath != null && message.fromcssPath != "") {
                        if (openrpadebug) console.log("fromele = document.querySelector('" + message.fromcssPath + "');");
                        fromele = document.querySelector(message.fromcssPath);
                    }
                    if (fromele == null) {
                        var test = JSON.parse(JSON.stringify(message));
                        if (openrpadebug) console.log("null hits when searching for anchor (from element!)");
                        return test;
                    }
                    if (openrpadebug) console.log(fromele);
                }
                var ele = [];
                if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                    message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
                }
                if (ele.length === 0 && message.xPath) {
                    //var iterator = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                    var iterator;
                    var xpath = message.xPath;
                    var searchfrom = document;
                    if (fromele) {
                        var prexpath = UTILS.xPath(fromele, true);
                        // xpath = prexpath + message.xPath.substr(1, message.xPath.length - 1);
                        xpath = prexpath + message.xPath;
                        searchfrom = document;
                    }
                    iterator = document.evaluate(xpath, searchfrom, null, XPathResult.ANY_TYPE, null);

                    if (openrpadebug) console.log("document.evaluate('" + xpath + "', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);");
                    try {
                        var thisNode = iterator.iterateNext();
                        if (thisNode && openrpadebug) console.log(thisNode);

                        while (thisNode) {
                            ele.push(thisNode);
                            thisNode = iterator.iterateNext();
                        }
                    }
                    catch (e) {
                        console.error('Error: Document tree modified during iteration ' + e);
                    }
                    if (ele.length === 0) message.xPath = 'false';
                    if (ele.length > 0) message.xPath = 'true';
                }
                if (ele.length === 0 && message.cssPath) {
                    if (fromele == null) {
                        ele = document.querySelectorAll(message.cssPath) as any;
                        if (openrpadebug) console.log("document.querySelector('" + message.cssPath + "');");
                    } else {
                        ele = fromele.querySelectorAll(message.cssPath);
                        if (openrpadebug) console.log("fromele.querySelector('" + message.cssPath + "');");
                    }

                    if (ele.length === 0) message.cssPath = 'false';
                    if (ele.length > 0) message.cssPath = 'true';
                }
                var base = Object.assign({}, message);
                message.results = [];
                notifyFrames(null);
                if (ele.length > 0) {
                    try {
                        for (var i = 0; i < ele.length; i++) {
                            var result = Object.assign({}, base);
                            if (message.data === 'getdom') {
                                result.result = openrpautil.mapDOM(ele[i], false, true);
                            }
                            else {
                                result.result = openrpautil.mapDOM(ele[i], false);
                            }
                            try {
                                openrpautil.applyPhysicalCords(result, ele[i]);
                            } catch (e) {
                                console.error(e);
                            }
                            result.zn_id = openrpautil.getuniqueid(ele[i]);

                            if (openrpautil.parent != null) {
                                result.parents = openrpautil.parent.parents + 1;
                                result.uix += openrpautil.parent.uix;
                                result.uiy += openrpautil.parent.uiy;
                                result.xpaths = openrpautil.parent.xpaths.slice(0);
                            } else if (inIframe()) {
                                // TODO: exit?
                                //return;
                                var currentFramePosition = openrpautil.currentFrameAbsolutePosition();
                                result.uix += currentFramePosition.x;
                                result.uiy += currentFramePosition.y;
                            }

                            message.results.push(result);
                        }
                    } catch (e) {
                        console.error(e);
                    }
                } else {
                }
            } catch (e) {
                console.error('error in getelements');
                message.error = e;
                console.error(e);
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug) console.log(test);
            return test;
        },
        getelement: function (message) {
            try {
                var ele = openrpautil.__getelement(message);
                if (ele !== null && ele !== undefined) {
                    try {
                        try {
                            openrpautil.applyPhysicalCords(message, ele);
                        } catch (e) {
                            console.error(e);
                        }
                    } catch (e) {
                        console.error(e);
                    }
                }
                if (ele !== null) {
                    if (message.data === 'getdom') {
                        message.result = openrpautil.mapDOM(ele, true, true, false);
                    }
                    else if (message.data === 'innerhtml') {
                        message.result = openrpautil.mapDOM(ele, true, true, true);
                    }
                    else {
                        message.result = openrpautil.mapDOM(ele, true);
                    }
                    message.zn_id = openrpautil.getuniqueid(ele);
                    if (openrpautil.parent != null) {
                        message.parents = openrpautil.parent.parents + 1;
                        message.uix += openrpautil.parent.uix;
                        message.uiy += openrpautil.parent.uiy;
                        message.xpaths = openrpautil.parent.xpaths.slice(0);
                    } else if (inIframe()) {
                        // TODO: exit?
                        //return;
                        var currentFramePosition = openrpautil.currentFrameAbsolutePosition();
                        message.uix += currentFramePosition.x;
                        message.uiy += currentFramePosition.y;
                    }
                }

            } catch (e) {
                console.error('error in getelement');
                message.error = e;
                console.error(e);
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug) console.log(test);
            return test;
        },
        __getelement: function (message) {
            openrpadebug = message.debug;
            if (message.uniquexpathids) openrpauniquexpathids = message.uniquexpathids;
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
            }
            if (ele === null && message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (openrpadebug) console.log("document.evaluate('" + message.xPath + "', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;");
                if (xpathEle === null) message.xPath = 'false';
                if (xpathEle !== null) message.xPath = 'true';
                ele = xpathEle;
            }
            if (ele === null && message.cssPath) {
                var cssEle = document.querySelector(message.cssPath);
                if (openrpadebug) console.log("document.querySelector('" + message.cssPath + "');");
                if (cssEle === null) message.cssPath = 'false';
                if (cssEle !== null) message.cssPath = 'true';
                ele = cssEle;
            }
            return ele;
        },
        updateelementvalue: function (message) {
            if (openrpadebug) console.log("before", message);
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
                var znEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (znEle === null) message.xPath = 'false';
                if (znEle !== null) message.xPath = 'true';
                ele = znEle;
            }
            if (ele === null && message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (xpathEle === null) message.xPath = 'false';
                if (xpathEle !== null) message.xPath = 'true';
                ele = xpathEle;
            }
            if (ele === null && message.cssPath) {
                var cssEle = document.querySelector(message.cssPath);
                if (cssEle === null) message.cssPath = 'false';
                if (cssEle !== null) message.cssPath = 'true';
                ele = cssEle;
            }
            if (ele) {
                var data = message.data;
                try {
                    data = Base64.decode(data);
                } catch (e) {
                    console.error(e);
                    console.log(data);
                }
                if (openrpadebug) console.log('focus', ele);
                ele.focus();
                if (ele.tagName == "INPUT" && ele.getAttribute("type") == "checkbox") {
                    if (data === true || data === "true" || data === "True") {
                        if (openrpadebug) console.log('set checked = true');
                        ele.checked = true;
                    } else {
                        if (openrpadebug) console.log('set checked = false');
                        ele.checked = false;
                    }
                } else if (message.result == "innerhtml") {
                    if (openrpadebug) console.log('set value', data);
                    ele.innerHTML = data;
                } else if (message.result == "textcontent") {
                    if (openrpadebug) console.log('set value', data);
                    ele.innerText = data;
                } else if (ele.tagName == "DIV") {
                    if (openrpadebug) console.log('set value', data);
                    ele.innerText = data;
                } else {
                    if (openrpadebug) console.log('set value', data);
                    ele.value = data;
                }
                try {
                    var evt = document.createEvent("HTMLEvents");
                    evt.initEvent("change", true, true);
                    ele.dispatchEvent(evt);
                } catch (e) {
                    console.error(e);
                }
                try {
                    var evt = document.createEvent("HTMLEvents");
                    evt.initEvent("input", true, true);
                    ele.dispatchEvent(evt);
                } catch (e) {
                    console.error(e);
                }
            }
            delete message.result;
            delete message.results;
            if (openrpadebug) console.log("after", message);
            var test = JSON.parse(JSON.stringify(message));
            return test;
        },
        updateelementvalues: function (message) {
            if (openrpadebug) console.log("before", message);
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
                var znEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (znEle === null) message.xPath = 'false';
                if (znEle !== null) message.xPath = 'true';
                ele = znEle;
            }
            if (ele === null && message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (xpathEle === null) message.xPath = 'false';
                if (xpathEle !== null) message.xPath = 'true';
                ele = xpathEle;
            }
            if (ele === null && message.cssPath) {
                var cssEle = document.querySelector(message.cssPath);
                if (cssEle === null) message.cssPath = 'false';
                if (cssEle !== null) message.cssPath = 'true';
                ele = cssEle;
            }
            if (ele) {
                var data = message.data;
                try {
                    data = Base64.decode(data);
                } catch (e) {
                    console.error(e);
                    console.log(data);
                }
                ele.focus();
                var values = JSON.parse(data);
                if (ele.tagName && ele.tagName.toLowerCase() == "select") {
                    for (let i = 0; i < ele.options.length; i++) {
                        if (values.indexOf(ele.options[i].value) > -1) {
                            ele.options[i].selected = true;
                        } else { ele.options[i].selected = false; }
                    }
                }

                try {
                    var evt = document.createEvent("HTMLEvents");
                    evt.initEvent("change", true, true);
                    ele.dispatchEvent(evt);
                } catch (e) {
                    console.error(e);
                }
                try {
                    var evt = document.createEvent("HTMLEvents");
                    evt.initEvent("input", true, true);
                    ele.dispatchEvent(evt);
                } catch (e) {
                    console.error(e);
                }
            }
            if (openrpadebug) console.log("after", message);
            delete message.result;
            delete message.results;
            var test = JSON.parse(JSON.stringify(message));
            return test;
        },
        applyPhysicalCords: function (message, ele) {
            try {
                var ClientRect = ele.getBoundingClientRect();
                var devicePixelRatio = window.devicePixelRatio || 1;
                var t = ele;
                var scrollLeft = (((t = document.documentElement) || (t = document.body.parentNode)) && typeof t.scrollLeft === 'number' ? t : document.body).scrollLeft;
                message.x = Math.floor(ClientRect.left);
                message.y = Math.floor(ClientRect.top);
                message.width = Math.floor(ele.offsetWidth);
                message.height = Math.floor(ele.offsetHeight);
                message.uiwidth = Math.round(ele.offsetWidth * devicePixelRatio);
                message.uiheight = Math.round(ele.offsetHeight * devicePixelRatio);
                if (window.self === window.top) {
                    message.uix = Math.round((ClientRect.left - scrollLeft) * devicePixelRatio);
                    message.uiy = Math.round((ClientRect.top * devicePixelRatio) + (window.outerHeight - (window.innerHeight * devicePixelRatio)));
                } else {
                    message.uix = Math.round(ClientRect.left * devicePixelRatio);
                    message.uiy = Math.round(ClientRect.top * devicePixelRatio);
                }
                if (inIframe() == false) {
                    var isAtMaxWidth = screen.availWidth - window.innerWidth === 0;
                    if (isAtMaxWidth) {
                        var isFirefox = typeof InstallTrigger !== 'undefined';
                        if (isFirefox) {
                            message.uix += 8;
                            message.uiy -= 7;
                        } else {
                            message.uix += 8;
                            message.uiy += 8;
                        }
                    } else {
                        message.uix += 7;
                        message.uiy -= 7;
                    }
                }
            } catch (error) {
                console.debug(error);
            }
        },
        // https://stackoverflow.com/questions/53056796/getboundingclientrect-from-within-iframe
        currentFrameAbsolutePosition: function () {
            let currentWindow = window;
            let currentParentWindow;
            let positions = [];
            let rect;
            if (inIframe()) {
            }
            currentParentWindow = parent;
            while (currentWindow !== window.top) {
                for (let idx = 0; idx < currentParentWindow.frames.length; idx++)
                    if (currentParentWindow.frames[idx] === currentWindow) {
                        // for (let frameElement of currentParentWindow.document.getElementsByTagName('iframe')) {
                        for (let t = 0; t < currentParentWindow.frames.length; t++) {
                            try {
                                let frameElement = currentParentWindow.frames[t];

                                if (typeof frameElement.getBoundingClientRect === "function") {
                                    rect = frameElement.getBoundingClientRect();

                                    positions.push({ x: rect.x, y: rect.y });
                                } else if (frameElement.frameElement != null && typeof frameElement.frameElement.getBoundingClientRect === "function") {
                                    rect = frameElement.frameElement.getBoundingClientRect();
                                    positions.push({ x: rect.x, y: rect.y });
                                } else if (frameElement.window != null && typeof frameElement.window.getBoundingClientRect === "function") {
                                    rect = frameElement.window.getBoundingClientRect();
                                    positions.push({ x: rect.x, y: rect.y });
                                } else if (frameElement.contentWindow === currentWindow) {
                                    rect = frameElement.getBoundingClientRect();

                                    positions.push({ x: rect.x, y: rect.y });
                                } else if (frameElement.window === currentWindow) {
                                    if (typeof frameElement.getBoundingClientRect === "function") {
                                        rect = frameElement.getBoundingClientRect();

                                        positions.push(rect);
                                    } else if (frameElement.frameElement != null && typeof frameElement.frameElement.getBoundingClientRect === "function") {
                                        rect = frameElement.frameElement.getBoundingClientRect();

                                        positions.push(rect);
                                    } else {
                                        positions.push({ x: 0, y: 0 });
                                    }

                                }
                            } catch (e) {
                                // console.debug(e);
                                // console.error(e);
                                break;
                            }
                        }
                        //for (let frameElement of currentParentWindow.frames) {
                        //}

                        currentWindow = currentParentWindow;
                        currentParentWindow = currentWindow.parent;
                        break;
                    }
            }

            var result = positions.reduce((accumulator, currentValue) => {
                return {
                    x: (accumulator.x + currentValue.x) | 0,
                    y: (accumulator.y + currentValue.y) | 0
                };
            }, { x: 0, y: 0 });
            return result;
        },
        getOffset: function (el) {
            var _x = 0;
            var _y = 0;
            while (el && !isNaN(el.offsetLeft) && !isNaN(el.offsetTop)) {
                _x += el.offsetLeft - el.scrollLeft;
                _y += el.offsetTop - el.scrollTop;
                el = el.offsetParent;
            }
            return { top: _y, left: _x };
        },
        pushEvent: function (action, event) {
            let frame: number | string = -1;
            if (window.frameElement) frame = window.frameElement.id;
            if (action === 'keydown') {
                var evt = { functionName: action, key: String.fromCharCode(event.which) };
                // if (openrpadebug) console.log("sendMessage", evt);
                chrome.runtime.sendMessage(evt);
            }
            else if (action === 'keyup') {
                var evt = { functionName: action, key: String.fromCharCode(event.which) };
                // if (openrpadebug) console.log("sendMessage", evt);
                chrome.runtime.sendMessage(evt);
            }
            else {
                // https://www.jeffersonscher.com/res/resolution.php

                // https://stackoverflow.com/questions/3437786/get-the-size-of-the-screen-current-web-page-and-browser-window

                var message = { functionName: action, frame: frame, parents: 0, xpaths: [], uix: "", uiy: "", cssPath: "", xPath: "", zn_id: "", c: "", result: "" };
                var targetElement = null;
                targetElement = event.target || event.srcElement;
                if (targetElement == null) {
                    if (openrpadebug) console.log('targetElement == null');
                    return;
                }
                try {
                    openrpautil.applyPhysicalCords(message, targetElement);
                } catch (e) {
                    console.error(e);
                }
                if (openrpautil.parent != null) {
                    message.parents = openrpautil.parent.parents + 1;
                    message.uix += openrpautil.parent.uix;
                    message.uiy += openrpautil.parent.uiy;
                    message.xpaths = openrpautil.parent.xpaths.slice(0);
                    //message.x += parent.uix;
                    //message.y += parent.uiy;
                    //message.width += parent.width;
                    //message.height += parent.height;
                } else if (inIframe()) {
                    // TODO: exit?
                    //return;
                    var currentFramePosition = openrpautil.currentFrameAbsolutePosition();
                    message.uix += currentFramePosition.x;
                    message.uiy += currentFramePosition.y;
                }
                message.cssPath = UTILS.cssPath(targetElement, false);
                message.xPath = UTILS.xPath(targetElement, true);
                message.zn_id = openrpautil.getuniqueid(targetElement);
                message.c = targetElement.childNodes.length;
                message.result = openrpautil.mapDOM(targetElement, true);
                message.xpaths.push(message.xPath);
                if (targetElement.contentWindow) {
                    if(disallowPost()) return;
                    var iframeWin = targetElement.contentWindow;
                    if (openrpadebug) console.log("Post message to iframe", message);
                    iframeWin.postMessage(message, '*');
                    if (openrpadebug) console.log('targetElement.tagName == iframe or frame');
                    return;
                }
                try {
                    // if (openrpadebug) console.log("sendMessage", message);
                    chrome.runtime.sendMessage(message);
                } catch (e) {
                    console.error(e);
                }
            }
        },
        getuniqueid: function (element) {
            if (element === null || element === undefined) return null;
            if (element.attributes === null || element.attributes === undefined) return null;
            for (var r = 0; r < element.attributes.length; r++) {
                var name = element.attributes[r].nodeName;
                if (name === 'zn_id') return element.attributes[r].nodeValue;
            }
            if (element === null || element === undefined) return null;
            if (element.attributes === null || element.attributes === undefined) return null;
            ++cachecount;
            element.setAttribute('zn_id', cachecount);
            return cachecount;
        },
        executescript: function (message) {
            // try {
            //     openrpadebug = message.debug;
            //     if (openrpadebug) console.log(message);
            //     if (openrpadebug) console.log('script', message.script);
            //     message.result = eval(message.script);
            //     if (openrpadebug) console.log('result', message.result);
            // } catch (e) {
            //     console.error(e);
            //     message.error = e;
            // }
            // delete message.script;
            // var test = JSON.parse(JSON.stringify(message));
            // if (openrpadebug) console.log(test);
            // return test;
            try {
                if (document) console.log('script', message.script);
                // message.result = eval(message.script);
                var s = document.createElement('script');
                s.async = false;
                s.src = message.script;
                s.addEventListener('load', function () {
                    // @ts-ignore
                    document.dynjsloaded = true;
                });
                document.body.appendChild(s);
                if (openrpadebug) console.log('result', message.result);
            } catch (e) {
                console.error(e);
                message.error = e;
            }
            delete message.script;
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug) console.log(test);
            return test;
        },
        fullPath: function (el) {
            var names = [];
            while (el.parentNode) {
                if (el.id) {
                    names.unshift('#' + el.id);
                    break;
                } else {
                    if (el === el.ownerDocument.documentElement) names.unshift(el.tagName);
                    else {
                        for (var c = 1, e = el; e.previousElementSibling; e = e.previousElementSibling, c++);
                        names.unshift(el.tagName + ":nth-child(" + c + ")");
                    }
                    el = el.parentNode;
                }
            }
            return names.join(" > ");
        },
        toJSON: function (node, maxiden, ident) {
            if (ident === null || ident === undefined) ident = 0;
            if (maxiden === null || maxiden === undefined) ident = 1;

            node = node || this;
            var obj = {
                nodeType: node.nodeType, tagName: "", nodeName: "", nodeValue: undefined, attributes: [], childNodes: []
            };
            if (node.tagName) {
                obj.tagName = node.tagName.toLowerCase();
            } else
                if (node.nodeName) {
                    obj.nodeName = node.nodeName;
                }
            if (node.nodeValue) {
                obj.nodeValue = node.nodeValue;
            }
            var attrs = node.attributes;
            if (attrs) {
                var length = attrs.length;
                var arr = obj.attributes = new Array(length);
                for (var i = 0; i < length; i++) {
                    let attr = attrs[i];
                    arr[i] = [attr.nodeName, attr.nodeValue];
                }
            }
            var childNodes = node.childNodes;
            if (childNodes && ident < maxiden) {
                length = childNodes.length;
                arr = obj.childNodes = new Array(length);
                for (i = 0; i < length; i++) {
                    arr[i] = openrpautil.toJSON(childNodes[i], maxiden, ident + 1);
                }
            }
            return obj;
        },
        toDOM: function (obj) {
            if (typeof obj === 'string') {
                obj = JSON.parse(obj);
            }
            var node, nodeType = obj.nodeType;
            switch (nodeType) {
                case 1: //ELEMENT_NODE
                    node = document.createElement(obj.tagName);
                    var attributes = obj.attributes || [];
                    for (var i = 0, len = attributes.length; i < len; i++) {
                        var attr = attributes[i];
                        node.setAttribute(attr[0], attr[1]);
                    }
                    break;
                case 3: //TEXT_NODE
                    node = document.createTextNode(obj.nodeValue);
                    break;
                case 8: //COMMENT_NODE
                    node = document.createComment(obj.nodeValue);
                    break;
                case 9: //DOCUMENT_NODE
                    node = document.implementation.createDocument(null, null);
                    break;
                case 10: //DOCUMENT_TYPE_NODE
                    node = document.implementation.createDocumentType(obj.nodeName, null, null);
                    break;
                case 11: //DOCUMENT_FRAGMENT_NODE
                    node = document.createDocumentFragment();
                    break;
                default:
                    return node;
            }
            if (nodeType === 1 || nodeType === 11) {
                var childNodes = obj.childNodes || [];
                for (i = 0, len = childNodes.length; i < len; i++) {
                    node.appendChild(openrpautil.toDOM(childNodes[i]));
                }
            }
            return node;
        },
        mapDOM: function (element, json, mapdom, innerhtml) {
            var maxiden = 40;
            if (mapdom !== true) maxiden = 1;
            if (maxiden === null || maxiden === undefined) maxiden = 20;
            var treeObject = {};
            // If string convert to document Node
            if (typeof element === "string") {
                let docNode: Document;
                if (window.DOMParser) {
                    let parser = new DOMParser();
                    docNode = parser.parseFromString(element, "text/xml");
                }
                element = docNode.firstChild;
            }
            //Recursively loop through DOM elements and assign properties to object
            function treeHTML(element, object, maxiden, ident?) {
                if (ident === null || ident === undefined) ident = 0;
                if (maxiden === null || maxiden === undefined) maxiden = 1;
                openrpautil.getuniqueid(element);
                object["tagName"] = element.tagName;
                if (ident === 0) {
                    object["xPath"] = UTILS.xPath(element, true);
                    object["cssPath"] = UTILS.cssPath(element, false);
                    if (object["tagName"] !== 'STYLE' && object["tagName"] !== 'SCRIPT' && object["tagName"] !== 'HEAD' && object["tagName"] !== 'HTML') {
                        if (element.innerText !== undefined && element.innerText !== null && element.innerText !== '') {
                            object["innerText"] = element.innerText;
                        }
                    }
                }
                var nodeList = element.childNodes;
                if (nodeList) {
                    if (nodeList.length) {
                        object["content"] = [];
                        for (var i = 0; i < nodeList.length; i++) {
                            if (nodeList[i].nodeType === 3) {
                                if (mapdom !== true) {
                                    if (object["tagName"] !== 'STYLE' && object["tagName"] !== 'SCRIPT' && object["tagName"] !== 'HEAD') {
                                        object["content"].push(nodeList[i].nodeValue);
                                    }
                                }
                            } else {
                                if (ident < maxiden) {
                                    object["content"].push({});
                                    treeHTML(nodeList[i], object["content"][object["content"].length - 1], maxiden, ident + 1);
                                }
                            }
                        }
                    }
                }
                if (element.attributes) {
                    if (element.attributes.length) {
                        // To read values of disabled objects, we need to undisable them
                        //if (element.disabled === true) {
                        //    console.log('removing disabled!!!!');
                        //    wasDisabled = true;
                        //    //element.disabled == false;
                        //    element.removeAttribute("disabled");
                        //}
                        var attributecount = 0;
                        if (element.attributes["zn_id"] == undefined || element.attributes["zn_id"] == null) {
                            var zn_id = openrpautil.getuniqueid(element);
                        }
                        object["zn_id"] = element.attributes["zn_id"].nodeValue;
                        for (var r = 0; r < element.attributes.length; r++) {
                            var name = element.attributes[r].nodeName;
                            var value = element.attributes[r].nodeValue;
                            // value, innertext
                            if (ident === 0) {
                                if (mapdom !== true || name.toLowerCase() === 'zn_id') {
                                    object[name] = value;
                                    ++attributecount;
                                }
                                //if (['zn_id', 'id', 'classname', 'name', 'tagname', 'href', 'src', 'alt', 'clientrects'].includes(name.toLowerCase())) {
                                //    //object["attributes"][name] = value;
                                //    object[name] = value;
                                //    ++attributecount;
                                //}
                            }
                            else if (ident > 0 && mapdom === true) {
                                if (name.toLowerCase() === 'zn_id') {
                                    //object["attributes"][name] = value;
                                    object[name] = value;
                                    ++attributecount;
                                }
                            }
                        }

                        //if (attributecount === 0) delete object["attributes"];
                        // if (wasDisabled === true) {
                        //     if (ident === 0) {
                        //         //element.disabled == true;
                        //         element.setAttribute("disabled", "true");
                        //     }
                        // }
                    }
                }
            }
            treeHTML(element, treeObject, maxiden);
            treeObject["value"] = element.value;
            treeObject["isvisible"] = openrpautil.isVisible(element);
            treeObject["display"] = openrpautil.display(element);
            treeObject["isvisibleonscreen"] = openrpautil.isVisibleOnScreen(element);
            treeObject["disabled"] = element.disabled;
            treeObject["innerText"] = element.innerText;
            if (innerhtml) {
                treeObject["innerhtml"] = element.innerHTML;
            }
            if (element.tagName == "INPUT" && element.getAttribute("type") == "checkbox") {
                treeObject["checked"] = element.checked;
            }
            if (element.tagName && element.tagName.toLowerCase() == "options") {
                treeObject["selected"] = element.selected;
            }
            if (element.tagName && element.tagName.toLowerCase() == "select") {
                var selectedvalues = [];
                for (let i = 0; i < element.options.length; i++) {
                    if (element.options[i].selected) {
                        selectedvalues.push(element.options[i].value);
                        treeObject["text"] = element.options[i].text;
                    }
                }
                treeObject["values"] = selectedvalues;
            }

            //updateelementtext
            if (treeObject["disabled"] === null || treeObject["disabled"] === undefined) treeObject["disabled"] = false;
            return json ? JSON.stringify(treeObject) : treeObject;
        },
        isVisibleOnScreen: function (elm) {
            var rect = elm.getBoundingClientRect();
            var viewHeight = Math.max(document.documentElement.clientHeight, window.innerHeight);
            return !(rect.bottom < 0 || rect.top - viewHeight >= 0);
        },
        isVisible: function (elm) {
            return elm.offsetWidth > 0 && elm.offsetHeight > 0;
        },
        display: function (elm) {
            return window.getComputedStyle(elm, null).getPropertyValue('display');
        },
        getFrameName: function (frame) {
            var frames = parent.frames,
                l = frames.length,
                name = null;
            for (var x = 0; x < l; x++) {
                if (frames[x] === frame) {
                    name = frames[x].name;
                }
            }
            return name;
        },
        screenInfo: function () {
            return {
                screen: {
                    // availTop: window.screen.availTop,
                    // availLeft: window.screen.availLeft,
                    availHeight: window.screen.availHeight,
                    availWidth: window.screen.availWidth,
                    colorDepth: window.screen.colorDepth,
                    height: window.screen.height,
                    // left: window.screen.left,
                    orientation: window.screen.orientation,
                    pixelDepth: window.screen.pixelDepth,
                    // top: window.screen.top,
                    width: window.screen.width
                },
                screenX: window.screenX,
                screenY: window.screenY,
                screenLeft: window.screenLeft,
                screenTop: window.screenTop
            };
        },
        getXPath(el) {
            let nodeElem = el;
            if (nodeElem.id && this.options.shortid) {
                return `//*[@id="${nodeElem.id}"]`;
            }
            const parts = [];
            while (nodeElem && nodeElem.nodeType === Node.ELEMENT_NODE) {
                let nbOfPreviousSiblings = 0;
                let hasNextSiblings = false;
                let sibling = nodeElem.previousSibling;
                while (sibling) {
                    if (sibling.nodeType !== Node.DOCUMENT_TYPE_NODE && sibling.nodeName === nodeElem.nodeName) {
                        nbOfPreviousSiblings++;
                    }
                    sibling = sibling.previousSibling;
                }
                sibling = nodeElem.nextSibling;
                while (sibling) {
                    if (sibling.nodeName === nodeElem.nodeName) {
                        hasNextSiblings = true;
                        break;
                    }
                    sibling = sibling.nextSibling;
                }
                const prefix = nodeElem.prefix ? nodeElem.prefix + ':' : '';
                const nth = nbOfPreviousSiblings || hasNextSiblings ? `[${nbOfPreviousSiblings + 1}]` : '';
                parts.push(prefix + nodeElem.localName + nth);
                nodeElem = nodeElem.parentNode;
            }
            return parts.length ? '/' + parts.reverse().join('/') : '';
        },
        dispatchKeyboardEvent(element, type, character, keyCode, charCode) {
            if (character == null) character = String.fromCharCode(charCode);
            if (charCode == null) charCode = character.charCodeAt(0);
            if (keyCode == null) keyCode = character.charCodeAt(0); // view: window,
            var event = new KeyboardEvent(type, { "bubbles": true, "cancelable": true, "key": character, "ctrlKey": false, "shiftKey": false, "altKey": false, "charCode": charCode, "keyCode": keyCode, });
            var doc = document.ownerDocument || document;
            if (element == null) element = doc;
            element.dispatchEvent(event);
        },
        dispatchInputEvent(element, type, inputType, data) {
            var event = new InputEvent(type, { inputType: inputType, data: data });
            // @ts-ignore
            event.simulated = true;
            var doc = document.ownerDocument || document;
            if (element == null) element = doc;
            element.dispatchEvent(event);
        },
        sendtext(message) {
            try {
                var data = message.data;
                try {
                    data = Base64.decode(data);
                } catch (e) {
                    console.error(e);
                    console.log(data);
                }
                const element = openrpautil.__getelement(message);
                console.log(element);
                if (element == null) { throw new Error('SendText, failed locating element'); }
                let parsekeys = false;
                if (message.reset == true) { element.value = ''; delete message.reset; }
                if (message.parsekeys == true) { parsekeys = true; delete message.parsekeys; }
                let specialkey = null;
                for (let i = 0; i < data.length; ++i) {
                    let character = data[i];
                    let keyCode = character.toUpperCase().charCodeAt(i);
                    let charCode = data.charCodeAt(i);
                    if (character == "{") {
                        specialkey = "";
                        continue;
                    } else if (character != "}" && specialkey != null) {
                        specialkey += character;
                        continue;
                    } else if (character == "}") {
                        character = undefined; keyCode = 0; charCode = 0;
                        if (openrpautil.keynames[specialkey] != null) {
                            keyCode = openrpautil.keynames[specialkey];
                        } else {
                            switch (specialkey.toLowerCase()) {
                                case "left55": keyCode = 37; break;
                                case "up55": keyCode = 38; break;
                                case "right55": keyCode = 39; break;
                                case "down55": keyCode = 40; break;
                            }
                        }
                    }
                    console.log("sendtext: " + character + " " + keyCode + " " + charCode);
                    openrpautil.dispatchKeyboardEvent(element, 'keydown', character, keyCode, charCode);
                    openrpautil.dispatchKeyboardEvent(element, 'keypress', character, keyCode, charCode);
                    openrpautil.dispatchInputEvent(element, "beforeinput", "insertText", character);
                    if (specialkey == null) element.value += character;
                    openrpautil.dispatchKeyboardEvent(element, 'keyup', character, keyCode, charCode);
                    specialkey == null
                }
            } catch (e) {
                console.error('error in getelements');
                message.error = e;
                console.error(e);
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug) console.log(test);
            return test;
        },
        settext(message) {
            message.reset = true;
            return openrpautil.sendtext(message);
        },
        sendkeys(message) {
            message.parsekeys = true;
            return openrpautil.sendtext(message);
        },

        keynames: {
            'break': 3,
            'backspace / delete': 8,
            'tab': 9,
            'clear': 12,
            'enter': 13,
            'shift': 16,
            'ctrl': 17,
            'alt': 18,
            'pause/break': 19,
            'caps lock': 20,
            'hangul': 21,
            'hanja': 25,
            'escape': 27,
            'conversion': 28,
            'non-conversion': 29, // or 235 ?
            'spacebar': 32,
            'page up': 33,
            'page down': 34,
            'end': 35,
            'leftarrow': 37,
            'left': 37,
            'uparrow': 38,
            'up': 38,
            'rightarrow': 39,
            'right': 39,
            'downarrow': 40,
            'down': 40,
            'select': 41,
            'print': 42,
            'execute': 43,
            'printscreen': 44,
            'prtsrc': 44,
            'insert': 45,
            'delete': 46,
            'help': 47,
            '0': 48,
            '1': 49,
            '2': 50,
            '3': 51,
            '4': 52,
            '5': 53,
            '6': 54,
            '7': 55,
            '8': 56,
            '9': 57,
            ':': 58,
            'ffsemicolon': 59,
            'equals': 59,
            '<': 60,
            'ffequals': 61,
            'ß': 63,
            'ff@': 64,
            'a': 65,
            'b': 66,
            'c': 67,
            'd': 68,
            'e': 69,
            'f': 70,
            'g': 71,
            'h': 72,
            'i': 73,
            'j': 74,
            'k': 75,
            'l': 76,
            'm': 77,
            'n': 78,
            'o': 79,
            'p': 80,
            'q': 81,
            'r': 82,
            's': 83,
            't': 84,
            'u': 85,
            'v': 86,
            'w': 87,
            'x': 88,
            'y': 89,
            'z': 90,
            'windows': 91,
            'windowskey': 91,
            'leftwindows': 91,
            'leftwindowskey': 91,
            '?': 91, // or 162 ?
            'searchkey': 91, // Windows Key / Left ? / Chromebook Search key
            'righttwindows': 92,
            'rightwindowskey': 92,
            'windowsmenu': 93, // 'Windows Menu / Right ?',
            'sleep': 95,
            'numpad 0': 96,
            'numpad 1': 97,
            'numpad 2': 98,
            'numpad 3': 99,
            'numpad 4': 100,
            'numpad 5': 101,
            'numpad 6': 102,
            'numpad 7': 103,
            'numpad 8': 104,
            'numpad 9': 105,
            'multiply': 106,
            'add': 107,
            'numpad period (firefox)': 108,
            'subtract': 109,
            'decimal point': 110,
            'divide': 111,
            'f1': 112,
            'f2': 113,
            'f3': 114,
            'f4': 115,
            'f5': 116,
            'f6': 117,
            'f7': 118,
            'f8': 119,
            'f9': 120,
            'f10': 121,
            'f11': 122,
            'f12': 123,
            'f13': 124,
            'f14': 125,
            'f15': 126,
            'f16': 127,
            'f17': 128,
            'f18': 129,
            'f19': 130,
            'f20': 131,
            'f21': 132,
            'f22': 133,
            'f23': 134,
            'f24': 135,
            'f25': 136,
            'f26': 137,
            'f27': 138,
            'f28': 139,
            'f29': 140,
            'f30': 141,
            'f31': 142,
            'f32': 143,
            'numlock': 144,
            'scroll lock': 145,
            'airplane mode': 151,
            '^': 160,
            '!': 161,
            '#': 163,
            '$': 164,
            'ù': 165,
            'pagebackward': 166,
            'pageforward': 167,
            'refresh': 168,
            'closingparen': 169,
            '*': 170,
            '~': 171,
            'homekey': 172,
            'home': 172, // or 36 ?
            'ffminus': 173,
            'minus': 173,
            'mute': 173,
            'unmute': 173,
            'decrease volume level': 174,
            'increase volume level': 175,
            'next': 176,
            'previous': 177,
            'stop': 178,
            'play/pause': 179,
            'email': 180,
            'mute/unmute (firefox)': 181,
            'decrease volume level (firefox)': 182,
            'increase volume level (firefox)': 183,
            'semi-colon / ñ': 186,
            'equal sign': 187,
            'comma': 188,
            'dash': 189,
            'period': 190,
            'forward slash / ç': 191,
            'grave accent / ñ / æ / ö': 192,
            '?, / or °': 193,
            'numpad period (chrome)': 194,
            'open bracket': 219,
            'back slash': 220,
            'close bracket / å': 221,
            'single quote / ø / ä': 222,
            '`': 223,
            'left or right ? key (firefox)': 224,
            'altgr': 225,
            '< /git >, left back slash': 226,
            'GNOME Compose Key': 230,
            'ç': 231,
            'XF86Forward': 233,
            'XF86Back': 234,
            'alphanumeric': 240,
            'hiragana/katakana': 242,
            'half-width/full-width': 243,
            'kanji': 244,
            'unlock trackpad (Chrome/Edge)': 251,
            'toggle touchpad': 255
        },
        gettablev1(message) {
            let data = message.data;
            // if data is string, parse it to json
            if (typeof data === 'string') {
                data = JSON.parse(data);
            }

            if (openrpadebug) console.debug('gettablev1', data);
            const domTabe = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
            if (domTabe == null) {
                console.error('Failed locating table', message.xPath);
                const test = JSON.parse(JSON.stringify(message));
                return test;
            }
            const GetFirst = (element, xpath, prop) => {
                let value = null;
                const node = document.evaluate(xpath, element, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (node != null) {
                    value = node[prop];
                    if (value == null || value == '') value = '';
                    value = value.split('\r').join('').split('\t').join('').split('\n').join('').trim();
                }
                return value;
            }
            const GetFirstText = (element, xpath) => {
                return GetFirst(element, xpath, 'textContent');
            }

            let rowsxpath = data.rowsxpath && data.rowsxpath != '' ? data.rowsxpath : '';
            let cellsxpath = data.cellsxpath && data.cellsxpath != '' ? data.cellsxpath : '';
            let cellxpath = data.cellxpath && data.cellxpath != '' ? data.cellxpath : '';

            let headerrowsxpath = data.headerrowsxpath && data.headerrowsxpath != '' ? data.headerrowsxpath : '';
            let headerrowxpath = data.headerrowxpath && data.headerrowxpath != '' ? data.headerrowxpath : '';
            let headerrowindex = data.headerrowindex ? data.headerrowindex : 0;
            let skiptypecheck = data.skiptypecheck != null && data.skiptypecheck != '' ? data.skiptypecheck : false;
            let isGoogleSearch = false;

            if (domTabe.nodeName.toLowerCase() == 'table') {
                rowsxpath = rowsxpath != '' ? rowsxpath : '//tr';
                cellsxpath = cellsxpath != '' ? cellsxpath : `//*[local-name()='td' or local-name()='th']`;
                cellxpath = cellxpath != '' ? cellxpath : '';
                headerrowsxpath = headerrowsxpath != '' ? headerrowsxpath : cellsxpath;
                headerrowxpath = headerrowxpath != '' ? headerrowxpath : '';
            } else if (domTabe.nodeName.toLowerCase() == 'div') {
                // @ts-ignore
                if (domTabe.id == 'rso') {
                    isGoogleSearch = true;
                    headerrowindex = -1;
                    rowsxpath = `//div[contains(concat(' ', normalize-space(@class), ' '), ' g ')]`;
                    // rowsxpath = `/div`;
                } else {
                    if (rowsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'row')]`) != null) {
                        rowsxpath = `//div[contains(@class, 'row')]`;
                    } else if (rowsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'tableRow')]`) != null) {
                        rowsxpath = `//div[contains(@class, 'tableRow')]`;
                    } else if (rowsxpath == '' && GetFirstText(domTabe, `//div[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'row')]`) != null) {
                        rowsxpath = `//div[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'row')]`;
                    } else if (rowsxpath == '') {
                        console.error('Could not autodetect row class', domTabe.nodeName);
                        const test = JSON.parse(JSON.stringify(message));
                        return test;
                    }
                    console.log('rowsxpath', rowsxpath);
                    if (cellsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'col')]`) != null) {
                        cellsxpath = `//div[contains(@class, 'col')]`;
                    } else if (cellsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'cell')]`) != null) {
                        cellsxpath = `//div[contains(@class, 'tableCell')]`;
                    } else if (cellsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'tableCell')]`) != null) {
                        cellsxpath = `//div[contains(@class, 'tableCell')]`;
                    } else if (cellsxpath == '' && GetFirstText(domTabe, `.//*[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'col')`) != null) {
                        cellsxpath = `//*[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'col')`;
                    } else if (cellsxpath == '' && GetFirstText(domTabe, `.//*[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'cell')`) != null) {
                        cellsxpath = `//*[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'cell')`;
                    } else {
                        console.error('Could not autodetect column class', domTabe.nodeName);
                        const test = JSON.parse(JSON.stringify(message));
                        return test;
                    }
                    console.log('cellsxpath', cellsxpath);
                }
                cellxpath = cellxpath != '' ? cellxpath : '';
                headerrowsxpath = headerrowsxpath != '' ? headerrowsxpath : cellsxpath;
                headerrowxpath = headerrowxpath != '' ? headerrowxpath : '';
            } else {
                console.error('Table is of unknown type', domTabe.nodeName);
                const test = JSON.parse(JSON.stringify(message));
                return test;
            }

            if (openrpadebug) console.debug('skiptypecheck', skiptypecheck);
            const headers = [];
            const table = [];
            const isFloat = (val) => {
                const floatRegex = /^-?\d+(?:[.,]\d*?)?$/;
                if (!floatRegex.test(val))
                    return false;

                const newval = parseFloat(val);
                if (isNaN(newval))
                    return false;
                return true;
            }

            const isInt = (val) => {
                const intRegex = /^-?\d+$/;
                if (!intRegex.test(val))
                    return false;

                const intVal = parseInt(val, 10);
                return parseFloat(val) == intVal && !isNaN(intVal);
            }

            if (openrpadebug) console.debug('Working with table', domTabe);
            const query = document.evaluate('.' + rowsxpath, domTabe, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null)
            if (openrpadebug) console.debug('found ' + query.snapshotLength + ' rows using ' + rowsxpath);
            if (isGoogleSearch) {
                headers.push(['Title']);
                headers.push(['URL']);
                headers.push(['Description']);
            }
            for (let i = 0; i < query.snapshotLength; i++) {
                const row = query.snapshotItem(i)
                let subquery = null;
                if (i == headerrowindex && !isGoogleSearch) {
                    if (openrpadebug) console.debug('headers row', row);
                    if (!data.headerrowsxpath || data.headerrowsxpath == '') {
                        subquery = document.evaluate('.//th', row, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null)
                        if (subquery.snapshotLength == 0) {
                            subquery = document.evaluate('.//td', row, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null)
                        }
                    } else {
                        subquery = document.evaluate('.' + headerrowsxpath, row, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null)
                    }

                    if (openrpadebug) console.debug('headers row found ' + subquery.snapshotLength + ' cells using ' + headerrowsxpath);
                    for (let y = 0; y < subquery.snapshotLength; y++) {
                        const cel = subquery.snapshotItem(y)
                        let _name = cel.textContent;
                        if (headerrowxpath != '') {
                            _name = '';
                            let __name = GetFirstText(cel, '.' + headerrowxpath)
                            if (__name != null && __name != '') _name = __name;
                        } else {
                            let __name = GetFirstText(cel, './span')
                            if (__name == null) __name = GetFirstText(cel, './b')
                            if (__name == null) __name = GetFirstText(cel, './strong')
                            if (__name == null) __name = GetFirstText(cel, './em')
                            if (__name == null) __name = GetFirstText(cel, './/span')
                            if (__name == null) __name = GetFirstText(cel, './/b')
                            if (__name == null) __name = GetFirstText(cel, './/strong')
                            if (__name == null) __name = GetFirstText(cel, './/descendant::div[last()]')
                            if (__name == null) __name = GetFirstText(cel, './/em')
                            if (__name != null && __name != '') _name = __name;
                        }
                        if (_name == null || _name == '') _name = '';
                        _name = _name.split('\r').join('').split('\t').join('').split('\n').join('').trim()
                        if (!_name || _name == '') _name = 'cell' + (y + 1);
                        headers.push(_name);
                    }
                    if (openrpadebug) console.debug('headers', headers)
                }
                if (i <= headerrowindex) continue;
                subquery = document.evaluate('.' + cellsxpath, row, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null)
                if (openrpadebug) console.log('row', i, 'found ' + subquery.snapshotLength + ' cells using ' + cellsxpath);
                const obj = {};
                let hadvalue = false;

                if (isGoogleSearch) {
                    const title = GetFirstText(row, './/h3')
                    const url = GetFirst(row, './/a', 'href')
                    let description = GetFirstText(row, `.//span[contains(concat(' ', normalize-space(@class), ' '), ' st ')]`)
                    if (description == null) description = GetFirstText(row, `.//span[contains(concat(' ', normalize-space(@class), ' '), ' f ')]`)
                    if (description == null) description = GetFirstText(row, `.//div[@data-content-feature='1']`)
                    // if (description == null) description = GetFirstText(row, './/cite')
                    // //span[@class='f']/following-sibling::text()
                    obj['Title'] = title;
                    obj['URL'] = url;
                    obj['Description'] = description;
                    hadvalue = true;
                } else {
                    for (let y = 0; y < subquery.snapshotLength; y++) {
                        let cell = subquery.snapshotItem(y)
                        let val = cell.textContent;
                        if (cellxpath != '') {
                            val = '';
                            let __val = GetFirstText(cell, '.' + cellxpath)
                            if (__val != null && __val != '') val = __val;
                        }

                        if (!val || val == '') val = '';
                        while (val.endsWith('\n')) val = val.substring(0, val.length - 1);
                        while (val.startsWith('\n')) val = val.substring(1, val.length);
                        while (val.endsWith('\t')) val = val.substring(0, val.length - 1);
                        while (val.startsWith('\t')) val = val.substring(1, val.length);
                        val = val.trim();
                        if (!skiptypecheck) {
                            const input = document.evaluate(`.//input[@type='checkbox']`, cell, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                            if (input != null) {
                                // @ts-ignore
                                val = input.checked
                            }
                            if (isFloat(val)) {
                                val = parseFloat(val);
                            } else if (isInt(val)) {
                                val = Number.parseInt(val);
                                // is boolean 
                            } else if (val == true || val == false) {
                                val = val;
                            } else if (val && val.toLowerCase() == 'true') {
                                val = true;
                            } else if (val && val.toLowerCase() == 'false') {
                                val = false;
                            } else {
                                // xpath find input of type checkbox and then check if it is checked
                            }
                        }
                        let name = 'cell' + (y + 1);
                        if (headers.length > y) { name = headers[y]; }
                        obj[name] = val;
                        if (val != '') hadvalue = true;
                    }
                }
                if (hadvalue) table.push(obj);
            }
            console.log(table);
            message.result = table;
            const test = JSON.parse(JSON.stringify(message));
            return test;
        } // end gettablev1
    };

    openrpautil.init();

    
}
if (typeof openrpautil === 'undefined' || openrpautil == null) {

    let url = document.URL;
    let ExcludeDomains = defaultExcludeDomains.join(",");
    chrome.storage.sync.get(
        { excludeDomains: defaultExcludeDomains.join(",") },
        (items) => {
            ExcludeDomains = items.excludeDomains.split(",");
            for(let i = 0; i < ExcludeDomains.length; i++) {
                let part = ExcludeDomains[i].trim();
                if (url.startsWith("http://" + part) || url.startsWith("https://" + part) || url.startsWith("http://www." + part) || url.startsWith("https://www." + part)) {
                    // console.debug("skip page", part);
                    return;
                }
            }
            doit();
        }
    );



    // https://chromium.googlesource.com/chromium/blink/+/master/Source/devtools/front_end/components/DOMPresentationUtils.js
    // https://gist.github.com/asfaltboy/8aea7435b888164e8563
    /*
     * Copyright (C) 2015 Pavel Savshenko
     * Copyright (C) 2011 Google Inc.  All rights reserved.
     * Copyright (C) 2007, 2008 Apple Inc.  All rights reserved.
     * Copyright (C) 2008 Matt Lilek <<EMAIL>>
     * Copyright (C) 2009 Joseph Pecoraro
     *
     * Redistribution and use in source and binary forms, with or without
     * modification, are permitted provided that the following conditions
     * are met:
     *
     * 1.  Redistributions of source code must retain the above copyright
     *     notice, this list of conditions and the following disclaimer.
     * 2.  Redistributions in binary form must reproduce the above copyright
     *     notice, this list of conditions and the following disclaimer in the
     *     documentation and/or other materials provided with the distribution.
     * 3.  Neither the name of Apple Computer, Inc. ("Apple") nor the names of
     *     its contributors may be used to endorse or promote products derived
     *     from this software without specific prior written permission.
     *
     * THIS SOFTWARE IS PROVIDED BY APPLE AND ITS CONTRIBUTORS "AS IS" AND ANY
     * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
     * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
     * DISCLAIMED. IN NO EVENT SHALL APPLE OR ITS CONTRIBUTORS BE LIABLE FOR ANY
     * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
     * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
     * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
     * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
     * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
     * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
     */

    var UTILS = {
        xPath: function (node, optimized) {
            if (node.nodeType === Node.DOCUMENT_NODE)
                return "/";
            var steps = [];
            var contextNode = node;
            while (contextNode) {
                var step = UTILS._xPathValue(contextNode, optimized);
                if (!step)
                    break; // Error - bail out early.
                steps.push(step);
                if (step.optimized)
                    break;
                contextNode = contextNode.parentNode;
            }
            steps.reverse();
            return (steps.length && steps[0].optimized ? "" : "/") + steps.join("/");
        },
        _xPathValue: function (node, optimized) {
            var ownValue;
            var ownIndex = UTILS._xPathIndex(node);
            if (ownIndex === -1)
                return null; // Error.
            switch (node.nodeType) {
                case Node.ELEMENT_NODE:
                    ownValue = node.localName;
                    if (optimized) {

                        for (var i = 0; i < openrpauniquexpathids.length; i++) {
                            var id = openrpauniquexpathids[i].toLowerCase();
                            if (node.getAttribute(id))
                                return new UTILS.DOMNodePathStep("//" + ownValue + "[@" + id + "=\"" + node.getAttribute(id) + "\"]", true);
                            id = id.toUpperCase();
                            if (node.getAttribute(id))
                                return new UTILS.DOMNodePathStep("//" + ownValue + "[@" + id + "=\"" + node.getAttribute(id) + "\"]", true);
                        }
                    }
                    if (optimized && node.getAttribute("id"))
                        return new UTILS.DOMNodePathStep("//" + ownValue + "[@id=\"" + node.getAttribute("id") + "\"]", true);
                    break;
                case Node.ATTRIBUTE_NODE:
                    ownValue = "@" + node.nodename;
                    break;
                case Node.TEXT_NODE:
                case Node.CDATA_SECTION_NODE:
                    ownValue = "text()";
                    break;
                case Node.PROCESSING_INSTRUCTION_NODE:
                    ownValue = "processing-instruction()";
                    break;
                case Node.COMMENT_NODE:
                    ownValue = "comment()";
                    break;
                case Node.DOCUMENT_NODE:
                    ownValue = "";
                    break;
                default:
                    ownValue = "";
                    break;
            }
            if (ownIndex > 0)
                ownValue += "[" + ownIndex + "]";
            return new UTILS.DOMNodePathStep(ownValue, node.nodeType === Node.DOCUMENT_NODE);
        },


        _xPathIndex: function (node) {
            // Returns -1 in case of error, 0 if no siblings matching the same expression, <XPath index among the same expression-matching sibling nodes> otherwise.
            function areNodesSimilar(left, right) {
                if (left === right)
                    return true;
                if (left.nodeType === Node.ELEMENT_NODE && right.nodeType === Node.ELEMENT_NODE)
                    return left.localName === right.localName;
                if (left.nodeType === right.nodeType)
                    return true;
                // XPath treats CDATA as text nodes.
                var leftType = left.nodeType === Node.CDATA_SECTION_NODE ? Node.TEXT_NODE : left.nodeType;
                var rightType = right.nodeType === Node.CDATA_SECTION_NODE ? Node.TEXT_NODE : right.nodeType;
                return leftType === rightType;
            }
            var siblings = node.parentNode ? node.parentNode.children : null;
            if (!siblings)
                return 0; // Root node - no siblings.
            var hasSameNamedElements;
            for (var i = 0; i < siblings.length; ++i) {
                if (areNodesSimilar(node, siblings[i]) && siblings[i] !== node) {
                    hasSameNamedElements = true;
                    break;
                }
            }
            if (!hasSameNamedElements)
                return 0;
            var ownIndex = 1; // XPath indices start with 1.
            for (var z = 0; z < siblings.length; ++z) {
                if (areNodesSimilar(node, siblings[z])) {
                    if (siblings[z] === node)
                        return ownIndex;
                    ++ownIndex;
                }
            }
            return -1; // An error occurred: |node| not found in parent's children.
        },
        cssPath: function (node, optimized) {
            if (node.nodeType !== Node.ELEMENT_NODE)
                return "";
            var steps = [];
            var contextNode = node;
            while (contextNode) {
                var step = UTILS._cssPathStep(contextNode, !!optimized, contextNode === node);
                if (!step)
                    break; // Error - bail out early.
                steps.push(step);
                if (step.optimized)
                    break;
                contextNode = contextNode.parentNode;
            }
            steps.reverse();
            return steps.join(" > ");
        },
        _cssPathStep: function (node, optimized, isTargetNode) {
            if (node.nodeType !== Node.ELEMENT_NODE)
                return null;

            var id = node.getAttribute("id");
            if (optimized) {
                if (id)
                    return new UTILS.DOMNodePathStep(idSelector(id), true);
                var nodeNameLower = node.nodeName.toLowerCase();
                if (nodeNameLower === "body" || nodeNameLower === "head" || nodeNameLower === "html")
                    return new UTILS.DOMNodePathStep(node.nodeName.toLowerCase(), true);
            }
            var nodeName = node.nodeName.toLowerCase();

            if (id && optimized)
                return new UTILS.DOMNodePathStep(nodeName.toLowerCase() + idSelector(id), true);
            var parent = node.parentNode;
            if (!parent || parent.nodeType === Node.DOCUMENT_NODE)
                return new UTILS.DOMNodePathStep(nodeName.toLowerCase(), true);
            function prefixedElementClassNames(node) {
                var classAttribute = node.getAttribute("class");
                if (!classAttribute)
                    return [];

                return classAttribute.split(/\s+/g).filter(Boolean).map(function (name) {
                    // The prefix is required to store "__proto__" in a object-based map.
                    return "$" + name;
                });
            }
            function idSelector(id) {
                return "#" + escapeIdentifierIfNeeded(id);
            }
            function escapeIdentifierIfNeeded(ident) {
                if (isCSSIdentifier(ident))
                    return ident;
                var shouldEscapeFirst = /^(?:[0-9]|-[0-9-]?)/.test(ident);
                var lastIndex = ident.length - 1;
                return ident.replace(/./g, function (c, i) {
                    return shouldEscapeFirst && i === 0 || !isCSSIdentChar(c) ? escapeAsciiChar(c, i === lastIndex) : c;
                });
            }
            function escapeAsciiChar(c, isLast) {
                return "\\" + toHexByte(c) + (isLast ? "" : " ");
            }
            function toHexByte(c) {
                var hexByte = c.charCodeAt(0).toString(16);
                if (hexByte.length === 1)
                    hexByte = "0" + hexByte;
                return hexByte;
            }
            function isCSSIdentChar(c) {
                if (/[a-zA-Z0-9_-]/.test(c))
                    return true;
                return c.charCodeAt(0) >= 0xA0;
            }
            function isCSSIdentifier(value) {
                return /^-?[a-zA-Z_][a-zA-Z0-9_-]*$/.test(value);
            }
            var prefixedOwnClassNamesArray = prefixedElementClassNames(node);
            var needsClassNames = false;
            var needsNthChild = false;
            var ownIndex = -1;
            var siblings = parent.children;
            for (var i = 0; (ownIndex === -1 || !needsNthChild) && i < siblings.length; ++i) {
                var sibling = siblings[i];
                if (sibling === node) {
                    ownIndex = i;
                    continue;
                }
                if (needsNthChild)
                    continue;
                if (sibling.nodeName.toLowerCase() !== nodeName.toLowerCase())
                    continue;

                needsClassNames = true;
                var ownClassNames = prefixedOwnClassNamesArray;
                var ownClassNameCount = 0;
                for (var name in ownClassNames)
                    ++ownClassNameCount;
                if (ownClassNameCount === 0) {
                    needsNthChild = true;
                    continue;
                }
                var siblingClassNamesArray = prefixedElementClassNames(sibling);
                for (var j = 0; j < siblingClassNamesArray.length; ++j) {
                    var siblingClass = siblingClassNamesArray[j];
                    if (ownClassNames.indexOf(siblingClass))
                        continue;
                    delete ownClassNames[siblingClass];
                    if (!--ownClassNameCount) {
                        needsNthChild = true;
                        break;
                    }
                }
            }

            var result = nodeName.toLowerCase();
            if (isTargetNode && nodeName.toLowerCase() === "input" && node.getAttribute("type") && !node.getAttribute("id") && !node.getAttribute("class"))
                result += "[type=\"" + node.getAttribute("type") + "\"]";
            if (needsNthChild) {
                result += ":nth-child(" + (ownIndex + 1) + ")";
            } else if (needsClassNames) {
                for (var prefixedName in prefixedOwnClassNamesArray)
                    // for (var prefixedName in prefixedOwnClassNamesArray.keySet())
                    result += "." + escapeIdentifierIfNeeded(prefixedOwnClassNamesArray[prefixedName].substr(1));
            }

            return new UTILS.DOMNodePathStep(result, false);
        },
        DOMNodePathStep: function (value, optimized) {
            this.value = value;
            this.optimized = optimized || false;
        }
    }
    UTILS.DOMNodePathStep.prototype = {
        toString: function () {
            return this.value;
        }
    };

}

function extend(destination, source) {
    for (var property in source)
        destination[property] = source[property];
    return destination;
}

var eventMatchers = {
    'HTMLEvents': /^(?:load|unload|abort|error|select|change|submit|reset|focus|blur|resize|scroll)$/,
    'MouseEvents': /^(?:click|dblclick|mouse(?:down|up|over|move|out))$/
}
var defaultOptions = {
    pointerX: 0,
    pointerY: 0,
    button: 0,
    ctrlKey: false,
    altKey: false,
    shiftKey: false,
    metaKey: false,
    bubbles: true,
    cancelable: true
}


function simulate(element, eventName) {
    var options = extend(defaultOptions, arguments[2] || {});
    var oEvent, eventType = null;

    for (var name in eventMatchers) {
        if (eventMatchers[name].test(eventName)) { eventType = name; break; }
    }

    if (!eventType)
        throw new SyntaxError('Only HTMLEvents and MouseEvents interfaces are supported');

    if (document.createEvent) {
        oEvent = document.createEvent(eventType);
        if (eventType == 'HTMLEvents') {
            oEvent.initEvent(eventName, options.bubbles, options.cancelable);
        }
        else {
            oEvent.initMouseEvent(eventName, options.bubbles, options.cancelable, document.defaultView,
                options.button, options.pointerX, options.pointerY, options.pointerX, options.pointerY,
                options.ctrlKey, options.altKey, options.shiftKey, options.metaKey, options.button, element);
        }
        element.dispatchEvent(oEvent);
    }
    else {
        options.clientX = options.pointerX;
        options.clientY = options.pointerY;
        var evt = (document as any).createEventObject();
        oEvent = extend(evt, options);
        element.fireEvent('on' + eventName, oEvent);
    }
    return element;
}
