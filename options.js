(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
// https://developer.chrome.com/docs/extensions/develop/ui/options-page
// Saves options to chrome.storage
var defaultExcludeDomains = ["ogs.google.com", "meet.google.com", "play.google.com", "docs.google.com", "keep.google.com", "chrome.google.com", "support.mozilla.org",
    "codefileclient.danid.dk", "applet.danid.dk", "nemlog-in.mitid.dk", "business.comcast.com"];
const saveOptions = () => {
    // @ts-ignore
    const excludeDomains = document.getElementById('excludeDomains').value;
    chrome.storage.sync.set({ excludeDomains: excludeDomains }, () => {
        // Update status to let user know options were saved.
        const status = document.getElementById('status');
        status.textContent = 'Options saved.';
        setTimeout(() => {
            status.textContent = '';
        }, 750);
    });
};
// Restores select box and checkbox state using the preferences
// stored in chrome.storage.
const restoreOptions = () => {
    chrome.storage.sync.get({ excludeDomains: defaultExcludeDomains.join(",") }, (items) => {
        // @ts-ignore
        document.getElementById('excludeDomains').value = items.excludeDomains;
        // Update status to let user know options were saved.
        const status = document.getElementById('status');
        status.textContent = 'Options loaded.';
        setTimeout(() => {
            status.textContent = '';
        }, 750);
    });
};
document.addEventListener('DOMContentLoaded', restoreOptions);
document.getElementById('save').addEventListener('click', saveOptions);

},{}]},{},[1])
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm5vZGVfbW9kdWxlcy9icm93c2VyLXBhY2svX3ByZWx1ZGUuanMiLCJzcmMvb3B0aW9ucy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtBQ0FBLHVFQUF1RTtBQUN2RSxrQ0FBa0M7QUFFbEMsSUFBSSxxQkFBcUIsR0FBRyxDQUFDLGdCQUFnQixFQUFFLGlCQUFpQixFQUFFLGlCQUFpQixFQUFFLGlCQUFpQixFQUFFLGlCQUFpQixFQUFFLG1CQUFtQixFQUFFLHFCQUFxQjtJQUNuSyx5QkFBeUIsRUFBRSxpQkFBaUIsRUFBRSxvQkFBb0IsRUFBRSxzQkFBc0IsQ0FBRSxDQUFBO0FBQzlGLE1BQU0sV0FBVyxHQUFHLEdBQUcsRUFBRTtJQUNyQixhQUFhO0lBQ2IsTUFBTSxjQUFjLEdBQUcsUUFBUSxDQUFDLGNBQWMsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLEtBQUssQ0FBQztJQUV2RSxNQUFNLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQ3JCLEVBQUUsY0FBYyxFQUFFLGNBQWMsRUFBRSxFQUNsQyxHQUFHLEVBQUU7UUFDSCxxREFBcUQ7UUFDckQsTUFBTSxNQUFNLEdBQUcsUUFBUSxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUNqRCxNQUFNLENBQUMsV0FBVyxHQUFHLGdCQUFnQixDQUFDO1FBQ3RDLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLENBQUMsV0FBVyxHQUFHLEVBQUUsQ0FBQztRQUMxQixDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7SUFDVixDQUFDLENBQ0YsQ0FBQztBQUNKLENBQUMsQ0FBQztBQUVGLCtEQUErRDtBQUMvRCw0QkFBNEI7QUFDNUIsTUFBTSxjQUFjLEdBQUcsR0FBRyxFQUFFO0lBQzFCLE1BQU0sQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FDckIsRUFBRSxjQUFjLEVBQUUscUJBQXFCLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLEVBQ25ELENBQUMsS0FBSyxFQUFFLEVBQUU7UUFDUixhQUFhO1FBQ2IsUUFBUSxDQUFDLGNBQWMsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUMsY0FBYyxDQUFDO1FBRXZFLHFEQUFxRDtRQUNyRCxNQUFNLE1BQU0sR0FBRyxRQUFRLENBQUMsY0FBYyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ2pELE1BQU0sQ0FBQyxXQUFXLEdBQUcsaUJBQWlCLENBQUM7UUFDdkMsVUFBVSxDQUFDLEdBQUcsRUFBRTtZQUNkLE1BQU0sQ0FBQyxXQUFXLEdBQUcsRUFBRSxDQUFDO1FBQzFCLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztJQUVWLENBQUMsQ0FDRixDQUFDO0FBQ0osQ0FBQyxDQUFDO0FBRUYsUUFBUSxDQUFDLGdCQUFnQixDQUFDLGtCQUFrQixFQUFFLGNBQWMsQ0FBQyxDQUFDO0FBQzlELFFBQVEsQ0FBQyxjQUFjLENBQUMsTUFBTSxDQUFDLENBQUMsZ0JBQWdCLENBQUMsT0FBTyxFQUFFLFdBQVcsQ0FBQyxDQUFDIiwiZmlsZSI6ImdlbmVyYXRlZC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzQ29udGVudCI6WyIoZnVuY3Rpb24oKXtmdW5jdGlvbiByKGUsbix0KXtmdW5jdGlvbiBvKGksZil7aWYoIW5baV0pe2lmKCFlW2ldKXt2YXIgYz1cImZ1bmN0aW9uXCI9PXR5cGVvZiByZXF1aXJlJiZyZXF1aXJlO2lmKCFmJiZjKXJldHVybiBjKGksITApO2lmKHUpcmV0dXJuIHUoaSwhMCk7dmFyIGE9bmV3IEVycm9yKFwiQ2Fubm90IGZpbmQgbW9kdWxlICdcIitpK1wiJ1wiKTt0aHJvdyBhLmNvZGU9XCJNT0RVTEVfTk9UX0ZPVU5EXCIsYX12YXIgcD1uW2ldPXtleHBvcnRzOnt9fTtlW2ldWzBdLmNhbGwocC5leHBvcnRzLGZ1bmN0aW9uKHIpe3ZhciBuPWVbaV1bMV1bcl07cmV0dXJuIG8obnx8cil9LHAscC5leHBvcnRzLHIsZSxuLHQpfXJldHVybiBuW2ldLmV4cG9ydHN9Zm9yKHZhciB1PVwiZnVuY3Rpb25cIj09dHlwZW9mIHJlcXVpcmUmJnJlcXVpcmUsaT0wO2k8dC5sZW5ndGg7aSsrKW8odFtpXSk7cmV0dXJuIG99cmV0dXJuIHJ9KSgpIiwiLy8gaHR0cHM6Ly9kZXZlbG9wZXIuY2hyb21lLmNvbS9kb2NzL2V4dGVuc2lvbnMvZGV2ZWxvcC91aS9vcHRpb25zLXBhZ2Vcbi8vIFNhdmVzIG9wdGlvbnMgdG8gY2hyb21lLnN0b3JhZ2VcblxudmFyIGRlZmF1bHRFeGNsdWRlRG9tYWlucyA9IFtcIm9ncy5nb29nbGUuY29tXCIsIFwibWVldC5nb29nbGUuY29tXCIsIFwicGxheS5nb29nbGUuY29tXCIsIFwiZG9jcy5nb29nbGUuY29tXCIsIFwia2VlcC5nb29nbGUuY29tXCIsIFwiY2hyb21lLmdvb2dsZS5jb21cIiwgXCJzdXBwb3J0Lm1vemlsbGEub3JnXCIsXG4gIFwiY29kZWZpbGVjbGllbnQuZGFuaWQuZGtcIiwgXCJhcHBsZXQuZGFuaWQuZGtcIiwgXCJuZW1sb2ctaW4ubWl0aWQuZGtcIiwgXCJidXNpbmVzcy5jb21jYXN0LmNvbVwiIF1cbmNvbnN0IHNhdmVPcHRpb25zID0gKCkgPT4ge1xuICAgIC8vIEB0cy1pZ25vcmVcbiAgICBjb25zdCBleGNsdWRlRG9tYWlucyA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdleGNsdWRlRG9tYWlucycpLnZhbHVlO1xuICBcbiAgICBjaHJvbWUuc3RvcmFnZS5zeW5jLnNldChcbiAgICAgIHsgZXhjbHVkZURvbWFpbnM6IGV4Y2x1ZGVEb21haW5zIH0sXG4gICAgICAoKSA9PiB7XG4gICAgICAgIC8vIFVwZGF0ZSBzdGF0dXMgdG8gbGV0IHVzZXIga25vdyBvcHRpb25zIHdlcmUgc2F2ZWQuXG4gICAgICAgIGNvbnN0IHN0YXR1cyA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdzdGF0dXMnKTtcbiAgICAgICAgc3RhdHVzLnRleHRDb250ZW50ID0gJ09wdGlvbnMgc2F2ZWQuJztcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgc3RhdHVzLnRleHRDb250ZW50ID0gJyc7XG4gICAgICAgIH0sIDc1MCk7XG4gICAgICB9XG4gICAgKTtcbiAgfTtcbiAgXG4gIC8vIFJlc3RvcmVzIHNlbGVjdCBib3ggYW5kIGNoZWNrYm94IHN0YXRlIHVzaW5nIHRoZSBwcmVmZXJlbmNlc1xuICAvLyBzdG9yZWQgaW4gY2hyb21lLnN0b3JhZ2UuXG4gIGNvbnN0IHJlc3RvcmVPcHRpb25zID0gKCkgPT4ge1xuICAgIGNocm9tZS5zdG9yYWdlLnN5bmMuZ2V0KFxuICAgICAgeyBleGNsdWRlRG9tYWluczogZGVmYXVsdEV4Y2x1ZGVEb21haW5zLmpvaW4oXCIsXCIpIH0sXG4gICAgICAoaXRlbXMpID0+IHtcbiAgICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnZXhjbHVkZURvbWFpbnMnKS52YWx1ZSA9IGl0ZW1zLmV4Y2x1ZGVEb21haW5zO1xuXG4gICAgICAgIC8vIFVwZGF0ZSBzdGF0dXMgdG8gbGV0IHVzZXIga25vdyBvcHRpb25zIHdlcmUgc2F2ZWQuXG4gICAgICAgIGNvbnN0IHN0YXR1cyA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdzdGF0dXMnKTtcbiAgICAgICAgc3RhdHVzLnRleHRDb250ZW50ID0gJ09wdGlvbnMgbG9hZGVkLic7XG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIHN0YXR1cy50ZXh0Q29udGVudCA9ICcnO1xuICAgICAgICB9LCA3NTApO1xuICAgICAgICBcbiAgICAgIH1cbiAgICApO1xuICB9O1xuICBcbiAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignRE9NQ29udGVudExvYWRlZCcsIHJlc3RvcmVPcHRpb25zKTtcbiAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3NhdmUnKS5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsIHNhdmVPcHRpb25zKTsiXX0=
