var port = null;
var portname = 'com.openrpa.msg';
var base_debug = false;
var back_debug = false;
chrome.action.onClicked.addListener(function(tab) {
   chrome.runtime.openOptionsPage();
});
function BaseOnPortMessage(message) {
   if (port == null) {
      console.warn("BaseOnPortMessage: port is null!");
      console.debug(message);
      return;
   }
   if (message === null || message === undefined) {
      console.warn("BaseOnPortMessage: Received null message!");
      return;
   }
   if (message.functionName === "ping") return;
   if (base_debug) console.debug("[baseresc][" + message.messageid + "]" + message.functionName);
   if (message.functionName === "backgroundscript") {
      try {
         message.result = "ok";
      } catch (e) {
         console.error(e);
         message.result = e;
      }
      return;
   }
};
function BaseOnPortDisconnect(message) {
   if (base_debug) console.debug("BaseOnPortDisconnect: " + message);
   port = null;
   if (chrome.runtime.lastError) {
      console.warn("BaseOnPortDisconnect: " + chrome.runtime.lastError.message);
      port = null;
      setTimeout(function () {
         Baseconnect();
      }, 1000);
      return;
   } else {
      if (base_debug) console.debug("BaseOnPortDisconnect from native port");
   }
}
function Baseconnect() {
   if (base_debug) console.debug("Baseconnect()");
   if (port !== null && port !== undefined) {
      try {
         if (port != null) port.onMessage.removeListener(BaseOnPortMessage);
         if (port != null) port.onDisconnect.removeListener(BaseOnPortDisconnect);
      } catch (e) {
         console.debug(e);
      }
   }
   if (port === null || port === undefined) {
      try {
         if (back_debug) console.debug("Connecting to " + portname);
         port = chrome.runtime.connectNative(portname);
      } catch (e) {
         console.error(e);
         port = null;
         return;
      }
   }
   if (port != null) port.onMessage.addListener(BaseOnPortMessage);
   if (port != null) port.onDisconnect.addListener(BaseOnPortDisconnect);

   if (chrome.runtime.lastError) {
      console.warn("Whoops.. " + chrome.runtime.lastError.message);
      port = null;
      return;
   } else {
      if (base_debug) console.debug("Connected to native port, request backgroundscript script");
   }

}
Baseconnect();


if (back_debug) console.debug('openrpa extension begin');
var openrpautil_script = '';
var portname = 'com.openrpa.msg';
var lastwindowId = 1;
var openrpadebug = false;

// Opera 8.0+ (tested on Opera 42.0)



declare var InstallTrigger: any;
// Firefox 1.0+ (tested on Firefox 45 - 53)
var isFirefox = typeof InstallTrigger !== 'undefined';

// Edge 20+ (tested on Edge 38.14393.0.0)
var isChromeEdge = navigator.appVersion.indexOf('Edge') > -1;
if (!isChromeEdge) isChromeEdge = navigator.appVersion.indexOf('Edg') > -1;

var isChrome = !isChromeEdge && !isFirefox;
var portname = 'com.openrpa.msg';

async function SendToTab(windowId, message) {
   try {
      var retry = false;
      try {
         if (back_debug) console.debug("SendToTab: send message to tab id " + message.tabid + " windowId " + windowId);
         message = await tabssendMessage(message.tabid, message);
      } catch (e) {
         if (back_debug) console.debug('tabssendMessage failed once');
         retry = true;
         console.warn(e);
      }
      if (retry) {
         await new Promise(r => setTimeout(r, 2000));
         if (back_debug) console.debug("retry: SendToTab: send message to tab id " + message.tabid + " windowId " + windowId);
         message = await tabssendMessage(message.tabid, message);
      }

      var allWindows = await windowsgetAll();
      var win = allWindows.filter(x => x.id == windowId);
      var currentWindow = allWindows[0];
      if (win.length > 0) currentWindow = win[0];
      if (message.uix && message.uiy) {
         message.uix += currentWindow.left;
         message.uiy += currentWindow.top;
      }
      if (message.results) {
         if (Array.isArray(message.results)) {
            message.results.forEach((item) => {
               if (item.uix && item.uiy) {
                  item.uix += currentWindow.left;
                  item.uiy += currentWindow.top;
               }
               item.windowId = windowId;
               item.tabid = message.tabid;
            });
         } else {
            delete message.results;
         }
      }

   } catch (e) {
      console.error(e);
      message.error = JSON.stringify(e);
   }
   return message;
}
async function OnPortMessage(message) {
   try {
      if (port == null) {
         console.warn("OnPortMessage: port is null!", message);
         return;
      }
      if (message === null || message === undefined) {
         console.warn("OnPortMessage: Received null message!");
         return;
      }
      if (message.functionName === "ping") {
         return;
      }
      if (isChrome) message.browser = "chrome";
      if (isFirefox) message.browser = "ff";
      if (isChromeEdge) message.browser = "edge";
      if (back_debug) console.debug("[resc][" + message.messageid + "]" + message.functionName);
      if (message.functionName === "openrpautilscript") {
         return;
      }
      if (message.functionName === "contentscript") {
         return;
      }

      if (message.functionName === "enumwindows") {
         await EnumWindows(message);
         if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName + " results: " + message.results.length);
         if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
         return;
      }
      if (message.functionName === "enumtabs") {
         await EnumTabs(message);
         var _result = "";
         message.results = message.results.filter(x => x.type != "popup" && x.state != "minimized");
         for (var i = 0; i < message.results.length; i++) {
            var _tab = message.results[i].tab;
            _result += "(tabid " + _tab.id + "/index:" + _tab.index + ") ";
         }
         if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName + " results: " + message.results.length + " " + _result);
         if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
         return;
      }
      if (message.functionName === "selecttab") {
         let tab: chrome.tabs.Tab = null;
         var tabsList = await tabsquery();
         if (tabsList.length == 0) {
            message.error = "selecttab, No tabs found!";
            if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
         }
         for (var i = 0; i < tabsList.length; i++) {
            if (tabsList[i].id == message.tabid) {
               tab = tabsList[i];
            }
         }
         if (tab == null) {
            message.error = "Tab " + message.tabid + " not found!";
            if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
         }
         await tabshighlight(tab.index);
         message.tab = tab;
         // message.tab = await tabsupdate(message.tabid, { highlighted: true });
         if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName + " " + message.tab.url);
         if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
         return;
      }
      if (message.functionName === "updatetab") {
         var tabsList = await tabsquery();
         if (tabsList.length == 0) {
            message.error = "updatetab, No tabs found!";
            if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
         }
         for (var i = 0; i < tabsList.length; i++) {
            if (tabsList[i].id == message.tabid) {
               if (tabsList[i].url == message.tab.url) {
                  delete message.tab.url;
               }
            }
         }

         delete message.tab.browser;
         delete message.tab.audible;
         delete message.tab.discarded;
         delete message.tab.favIconUrl;
         delete message.tab.height;
         delete message.tab.id;
         delete message.tab.incognito;
         delete message.tab.status;
         delete message.tab.title;
         delete message.tab.width;
         delete message.tab.index;
         delete message.tab.windowId;
         message.tab = await tabsupdate(message.tabid, message.tab);
         if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName + " " + message.tab.url);
         if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
         return;
      }
      if (message.functionName === "closetab") {
         chrome.tabs.remove(message.tabid);
         if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
         return;
      }
      if (message.functionName === "openurl") {
         var windows = await windowsgetAll();
         if (back_debug) console.debug("openurl windowid" + message.windowId + " tabid: " + message.tabid, message);
         if (back_debug) console.debug("windows", windows);
         if (message.xPath == "true") {
            let createProperties = { url: message.data, windowId: undefined };
            var w = await getCurrentWindow();
            if (back_debug) console.debug("WINDOW", w);
            createProperties.windowId = w.id;
            if (back_debug) console.debug("openurl, open NEW in window " + w.id);
            //if (message.windowId !== null && message.windowId !== undefined && message.windowId > 0) createProperties.windowId = message.windowId;
            var newtab = await tabscreate(createProperties);
            message.tab = newtab;
            message.tabid = newtab.id;
            if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName);
            if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
         }
         if (message.windowId == -1) {
            var w = await getCurrentWindow();
            message.windowId = w.id;
         } else {
            windows = windows.filter(x => x.type != "popup" && x.state != "minimized");
            var win = windows.filter(x => x.id == message.windowId);
            if (win.length == 0) {
               var w = await getCurrentWindow();
               message.windowId = w.id;
            }
         }
         var tabsList = await tabsquery({ windowId: message.windowId });
         if (message.windowId !== null && message.windowId !== undefined && message.windowId !== '' && message.windowId > 0) {
            tabsList = tabsList.filter(x => x.windowId == message.windowId);
         } else {
            tabsList = tabsList.filter(x => x.windowId == lastwindowId);
         }
         if (message.tabid !== null && message.tabid !== undefined && message.tabid !== '' && message.tabid > 0) {
            tabsList = tabsList.filter(x => x.id == message.tabid);
         } else {
            tabsList = tabsList.filter(x => x.active == true);
         }
         if (tabsList.length == 0) {
            var w = await getCurrentWindow();
            // tabsList = await tabsquery({ windowId: w.id });
            let createProperties = { url: message.data, windowId: -1 };
            createProperties.windowId = w.id;
            if (back_debug) console.debug("openurl, open NEW 2 in window " + w.id);
            var newtab = await tabscreate(createProperties);
            if (back_debug) console.debug(newtab);
            message.tab = newtab;
            message.tabid = newtab.id;
            if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName);
            if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
            // message.error = "openurl, No tabs found!";
            // if(port!=null) port.postMessage(JSON.parse(JSON.stringify(message)));
            // return;
         }
         let tab: chrome.tabs.Tab = tabsList[0];
         if (tab.url != message.data) {
            let updateoptions = { active: true, highlighted: true, url: "" };
            updateoptions.url = message.data;
            if (back_debug) console.debug("openurl, update tab #" + tab.id, tab);
            lastwindowId = tab.windowId;
            tab = await tabsupdate(tab.id, updateoptions);
         } else {
            let updateoptions = { active: true, highlighted: true, url: "" };
            updateoptions.url = message.data;
            if (back_debug) console.debug("openurl, tab #" + tab.id + " windowId " + tab.windowId + " is already on the right url", tab);
            lastwindowId = tab.windowId;
            tab = await tabsupdate(tab.id, updateoptions);
         }
         message.tab = tab;
         message.tabid = tab.id;
         if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName);
         if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
         return;
      }
      if (back_debug) console.debug("SendToTab before tab #" + message.tabid + " windowId " + message.windowId, tab);
      var windowId = lastwindowId;
      var tabsList = await tabsquery();
      if (message.windowId !== null && message.windowId !== undefined && message.windowId !== '' && message.windowId > 0) {
         windowId = message.windowId;
         tabsList = tabsList.filter(x => x.windowId == message.windowId);
      } else {
         var templist = tabsList.filter(x => x.windowId == lastwindowId);
         if (templist.length > 0) {
            windowId = lastwindowId;
            tabsList = tabsList.filter(x => x.windowId == lastwindowId);
         }
      }
      if (message.tabid !== null && message.tabid !== undefined && message.tabid !== '' && message.tabid > 0) {
         tabsList = tabsList.filter(x => x.id == message.tabid);
      } else {
         tabsList = tabsList.filter(x => x.active == true);
      }
      if (tabsList.length == 0) {
         var w = await getCurrentWindow();
         tabsList = await tabsquery({ windowId: w.id });
         if (tabsList.length == 0) {
            message.error = "SendToTab, No tabs found! windowId " + message.windowId + " lastwindowId: " + lastwindowId;
            if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName + " No tabs found");
            if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
         }
         windowId = w.id;
      }
      var tab = tabsList[0];
      if (back_debug) console.debug("SendToTab tab #" + tab.id + " windowid " + tab.windowId, tab);
      message.windowId = windowId;
      message.tabid = tab.id;
      openrpadebug = message.debug;

      if (message.functionName === "executescript") {
         if (back_debug) console.debug("executescript tab #" + message.tabid + " frameId: " + message.frameId, message);
         // var script = "(" + message.script + ")()";
         var script = message.script;

         var detatch = false;
         try {
            if (back_debug) console.debug("attach to " + message.tabid);
            await debuggerattach(message.tabid);
            detatch = true;
            if (back_debug) console.debug("eval", message.script);
            message.result = await debuggerEvaluate(message.tabid, script);
            if (message.result && message.result.result && message.result.result.value) {
               message.result = [message.result.result.value];
            } else if (!Array.isArray(message.result)) {
               message.result = [message.result];
            }

            if (back_debug) console.debug("result", message.result);
            // message.results = [message.result];
         } catch (e) {
            console.error(e);
            message.error = e.message;
         }
         if (detatch) {
            try {
               if (back_debug) console.debug("detach to " + message.tabid);
               await debuggerdetach(message.tabid);
            } catch (e) {
               console.error(e);
            }
         }

         if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName);
         port.postMessage(JSON.parse(JSON.stringify(message)));
         return;
      }

      //if (message.functionName === "executescript") {
      //    if(back_debug) console.debug("executescript tab #" + message.tabid + " frameId: " + message.frameId, message);

      //    var detatch = false;
      //    try {
      //        if(back_debug) console.debug("attach to " + message.tabid);
      //        await debuggerattach(message.tabid);
      //        detatch = true;
      //        if(back_debug) console.debug("eval", message.script);
      //        message.result = await debuggerEvaluate(message.tabid, message.script);
      //        if (message.result && message.result.result && message.result.result.value) {
      //            message.result = message.result.result.value;
      //        }

      //        if(back_debug) console.debug("result", message.result);
      //        message.results = [message.result];
      //    } catch (e) {
      //        console.error(e);
      //        message.error = e.message;
      //    }
      //    if (detatch) {
      //        try {
      //            if(back_debug) console.debug("detach to " + message.tabid);
      //            await debuggerdetach(message.tabid);
      //        } catch (e) {
      //            console.error(e);
      //        }
      //    }
      //    if(back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName);
      //    if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
      //    return;
      //}

      message = await SendToTab(windowId, message);

   } catch (e) {
      console.error(e);
      message.error = JSON.stringify(e);
   }
   if (back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName);
   if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
   if (back_debug) console.debug(message);
};
function OnPortDisconnect(message) {
   if (back_debug) console.debug("OnPortDisconnect", message);
   port = null;
   if (chrome.runtime.lastError) {
      console.warn("onDisconnect: " + chrome.runtime.lastError.message);
      port = null;
      setTimeout(function () {
         connect();
      }, 1000);
      return;
   } else {
      if (back_debug) console.debug("onDisconnect from native port");
   }
}
function connect() {
   if (back_debug) console.debug("connect()");
   if (port !== null && port !== undefined) {
      try {
         if (port != null) port.onMessage.removeListener(OnPortMessage);
         if (port != null) port.onDisconnect.removeListener(OnPortDisconnect);
      } catch (e) {
         console.error(e);
      }
   }
   if (port === null || port === undefined) {
      try {
         if (back_debug) console.debug("Connecting to " + portname);
         port = chrome.runtime.connectNative(portname);
      } catch (e) {
         console.error(e);
         port = null;
         return;
      }
   }
   if (port != null) port.onMessage.addListener(OnPortMessage);
   if (port != null) port.onDisconnect.addListener(OnPortDisconnect);

   if (chrome.runtime.lastError) {
      console.warn("Whoops.. " + chrome.runtime.lastError.message);
      port = null;
      return;
   } else {
      if (back_debug) console.debug("Connected to native port");
   }
}
async function EnumTabs(message) {
   if (isChrome) message.browser = "chrome";
   if (isFirefox) message.browser = "ff";
   if (isChromeEdge) message.browser = "edge";
   message.results = [];
   var tabsList = await tabsquery();
   tabsList.forEach((tab) => {
      var _message = { functionName: "tabcreated", tabid: tab.id, tab: tab, browser: "chrome" };
      if (isChrome) _message.browser = "chrome";
      if (isFirefox) _message.browser = "ff";
      if (isChromeEdge) _message.browser = "edge";
      message.results.push(_message);
   });
}
async function EnumWindows(message) {
   var allWindows = await windowsgetAll();
   message.results = [];
   for (var i in allWindows) {
      var window = allWindows[i];
      var _message = { functionName: "windowcreated", windowId: window.id, result: JSON.stringify(window), browser: "chrome" };
      if (isChrome) _message.browser = "chrome";
      if (isFirefox) _message.browser = "ff";
      if (isChromeEdge) _message.browser = "edge";
      message.results.push(_message);
   }
}
async function OnPageLoad() {
   // if (window) window.removeEventListener("load", OnPageLoad, false);
   chrome.windows.onCreated.addListener((window) => {
      if (window.type === "normal" || window.type === "popup") { // panel
         if (window.type === "popup" && window.state == "minimized") return;
         if (window.id > 0) lastwindowId = window.id;
         var message = { functionName: "windowcreated", windowId: window.id, result: JSON.stringify(window), browser: "chrome" };
         if (isChrome) message.browser = "chrome";
         if (isFirefox) message.browser = "ff";
         if (isChromeEdge) message.browser = "edge";
         if (back_debug) console.debug("[send]" + message.functionName + " " + window.id);
         if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
      }
   });
   chrome.windows.onRemoved.addListener((windowId) => {
      var message = { functionName: "windowremoved", windowId: windowId, browser: "chrome" };
      if (isChrome) message.browser = "chrome";
      if (isFirefox) message.browser = "ff";
      if (isChromeEdge) message.browser = "edge";
      if (back_debug) console.debug("[send]" + message.functionName + " " + windowId);
      if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
   });
   chrome.windows.onFocusChanged.addListener((windowId) => {
      var message = { functionName: "windowfocus", windowId: windowId, browser: "chrome" };
      if (windowId > 0) lastwindowId = windowId;
      if (isChrome) message.browser = "chrome";
      if (isFirefox) message.browser = "ff";
      if (isChromeEdge) message.browser = "edge";
      if (back_debug) console.debug("[send]" + message.functionName + " " + windowId);
      if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
   });

   if (port == null) return;
}
async function tabsOnCreated(tab) {
   if (port == null) return;
   var message = { functionName: "tabcreated", tab: tab, tabid: tab.id, browser: "chrome" };
   if (isChrome) message.browser = "chrome";
   if (isFirefox) message.browser = "ff";
   if (isChromeEdge) message.browser = "edge";
   if (back_debug) console.debug("[send]" + message.functionName);
   if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
}
function tabsOnRemoved(tabId) {
   if (port == null) return;
   var message = { functionName: "tabremoved", tabid: tabId, browser: "chrome" };
   if (isChrome) message.browser = "chrome";
   if (isFirefox) message.browser = "ff";
   if (isChromeEdge) message.browser = "edge";
   if (back_debug) console.debug("[send]" + message.functionName);
   if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
}
async function tabsOnUpdated(tabId, changeInfo, tab) {
   if (!allowExecuteScript(tab)) {
      if (back_debug) console.debug('tabsOnUpdated, skipped, not allowExecuteScript');
      return;
   }
   if (openrpadebug) console.debug(changeInfo);
   try {
      if (back_debug) console.debug("tabsOnUpdated: " + changeInfo.status)
      if (openrpautil_script != null) {
         // tabsexecuteScript(tab.id, { code: openrpautil_script, allFrames: true });
      }
   } catch (e) {
      //    console.debug(e);
      //    if(back_debug) console.debug(tab);
      return;
   }
   var message = { functionName: "tabupdated", tabid: tabId, tab: tab, browser: "chrome" };
   if (isChrome) message.browser = "chrome";
   if (isFirefox) message.browser = "ff";
   if (isChromeEdge) message.browser = "edge";
   if (back_debug) console.debug("[send]" + message.functionName);
   if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
}
function tabsOnActivated(activeInfo) {
   if (port == null) return;
   var message = { functionName: "tabactivated", tabid: activeInfo.tabId, windowId: activeInfo.windowId, browser: "chrome" };
   if (isChrome) message.browser = "chrome";
   if (isFirefox) message.browser = "ff";
   if (isChromeEdge) message.browser = "edge";
   if (back_debug) console.debug("[send]" + message.functionName);
   if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
}

var allowExecuteScript = function (tab) {
   if (port == null) return false;
   if (tab.url == null) return false;
   if (isFirefox) {
      if (tab.url.startsWith("about:")) return false;
      if (tab.url.startsWith("https://support.mozilla.org")) return false;
   }
   // ff uses chrome:// when debugging ?
   if (tab.url.startsWith("chrome://")) return false;
   if (isChrome) {
      if (tab.url.startsWith("https://chrome.google.com")) return false;
   }
   if (tab.url.startsWith("https://docs.google.com/spreadsheets/d")) {
      if (back_debug) console.debug("skip google docs");
      return false;
   }
   return true;
}
var getCurrentWindow = function (): Promise<chrome.windows.Window> {
   return new Promise(function (resolve, reject) {
      chrome.windows.getCurrent(async curWindow => {
         if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError.message);
            return;
         }
         if (curWindow.type == "popup" && curWindow.state == "minimized") {
            if (back_debug) console.debug("Current window #" + curWindow.id + " looks like a background page, look for another window")
            var windows = await windowsgetAll();
            windows = windows.filter(x => x.type != "popup" && x.state != "minimized");
            if (windows.length > 0) return resolve(windows[0]);
         }
         resolve(curWindow);
      });
   });
}
var tabsquery = function (options?: any): Promise<chrome.tabs.Tab[]> {
   return new Promise(function (resolve, reject) {
      try {
         if (options === null || options === undefined) options = {};
         chrome.tabs.query(options, async (tabsList) => {
            if (chrome.runtime.lastError) {
               reject(chrome.runtime.lastError.message);
               return;
            }
            return resolve(tabsList);
            // fix: https://stackoverflow.com/questions/59974414/chrome-tabs-query-returning-empty-tab-array
         });
      } catch (e) {
         reject(e);
      }
   });
};
var windowsgetAll = function (): Promise<chrome.windows.Window[]> {
   return new Promise(function (resolve, reject) {
      try {
         chrome.windows.getAll({ populate: false }, (allWindows) => {
            if (chrome.runtime.lastError) {
               reject(chrome.runtime.lastError.message);
               return;
            }
            resolve(allWindows);
         });
      } catch (e) {
         reject(e);
      }
   });
};
// @ts-ignore
var tabsexecuteScript = function (tabid, options): Promise<chrome.scripting.InjectionResult[]> {
   return new Promise(function (resolve, reject) {
      try {
         // https://blog.bitsrc.io/what-is-chrome-scripting-api-f8dbdb6e3987
         var opt = Object.assign(options, { target: { tabId: tabid, allFrames: true } });
         if (opt.code) {
            opt.func = eval(opt.code);
            delete opt.code;
         }
         chrome.scripting.executeScript(
            opt, function (results) {
               if (chrome.runtime.lastError) {
                  reject(chrome.runtime.lastError.message);
                  return;
               }
               resolve(results);
            });
      } catch (e) {
         reject(e);
      }
   });
};
var getCurrentTab = function (): Promise<chrome.tabs.Tab> {
   return new Promise(function (resolve, reject) {
      try {
         chrome.tabs.getCurrent((tab) => {
            if (chrome.runtime.lastError) {
               reject(chrome.runtime.lastError.message);
               return;
            }
            resolve(tab);
         });
      } catch (e) {
         reject(e);
      }
   });
};
var tabsupdate = function (tabid, updateoptions): Promise<chrome.tabs.Tab> {
   return new Promise(function (resolve, reject) {
      try {
         chrome.tabs.update(tabid, updateoptions, (tab) => {
            if (chrome.runtime.lastError) {
               reject(chrome.runtime.lastError.message);
               return;
            }
            resolve(tab);
         });
      } catch (e) {
         reject(e);
      }
   });
};
var tabshighlight = function (index): Promise<void> {
   return new Promise(function (resolve, reject) {
      try {
         chrome.tabs.highlight({ 'tabs': index, windowId: lastwindowId }, () => {
            if (chrome.runtime.lastError) {
               reject(chrome.runtime.lastError.message);
               return;
            }
            resolve();
         });
      } catch (e) {
         reject(e);
      }
   });
};

var tabssendMessage = function (tabid, message) {
   return new Promise(async function (resolve, reject) {
      try {
         var details = [];
         if (message.frameId == -1) {
            details = await getAllFrames(tabid);
            if (back_debug) console.debug("tabssendMessage, found " + details.length + " frames in tab " + tabid);
         } else {
            if (back_debug) console.debug("tabssendMessage, sending to tab " + tabid + " frameid " + message.frameId);
         }
         var result = null;
         var lasterror = "tabssendMessage: No error";
         if (details.length > 1) {
            for (var i = 0; i < details.length; i++) {
               try {
                  var frame = details[i];
                  if (!allowExecuteScript(frame)) continue;
                  var tmp = await TabsSendMessage(tabid, message, { frameId: frame.frameId });
                  if (result == null) {
                     result = tmp;
                     result.frameId = frame.frameId;
                     if (result.result != null) {
                        result.frameId = frame.frameId;
                     }
                     if (result.results != null && result.results.length > 0) {
                        for (var z = 0; z < result.results.length; z++) {
                           result.results[z].frameId = frame.frameId;
                        }
                     }
                  } else {
                     if (result.result != null || result.result != undefined) {
                        //if (typeof result.result == "string") {
                        //    result.results = [JSON.parse(result.result)];
                        //} else {
                        //    result.results = [result.result];
                        //}                            
                        //delete result.result;
                     }
                     if (tmp.result != null) {
                        tmp.result.frameId = frame.frameId;
                        if (result.results == null) result.results = [];
                        result.results.push(tmp.result);
                     }
                     if (tmp.results != null && tmp.results.length > 0) {
                        for (var z = 0; z < tmp.results.length; z++) {
                           tmp.results[z].frameId = frame.frameId;
                        }
                        result.results = result.results.concat(tmp.results);
                     }
                  }
               } catch (e) {
                  lasterror = e;
                  console.debug(e);
               }
            }
         }
         if (details.length == 0 || details.length == 1) {
            lasterror = null;
            try {
               if (message.frameId > -1) result = await TabsSendMessage(tabid, message, { frameId: message.frameId });
               if (message.frameId == -1) result = await TabsSendMessage(tabid, message);
            } catch (e) {
               lasterror = e;
            }
            if (result == null) {
               try {
                  if (back_debug) console.debug("result == null, so send to tab id #" + tabid);
                  result = await TabsSendMessage(tabid, message);
                  if (result != null) lasterror = null;
               } catch (e) {
                  if (lasterror == null) lasterror = e;
               }
            }
            if (result == null) {
               var w = await getCurrentWindow();
               let tabsList = await tabsquery({ windowId: w.id });
               if (tabsList.length > 0) {
                  var tab = tabsList[0];
                  if (back_debug) console.debug("result == null, still null default window is #" + w.id + " and first tab is #" + tab.id + " so send to that!");
                  if (back_debug) console.debug("window", w);
                  if (back_debug) console.debug("tabsList", tabsList);
                  result = await TabsSendMessage(tab.id, message);
               }
            }
         }
         if (result == null || result == undefined) {
            // this will fail with "Could not establish connection. Receiving end does not exist." when page is loading, so just send empty result to robot, it will try again
            //if(back_debug) console.debug("tabssendMessage has no result, return original message");
            //message.error = lasterror;
            result = message;
            if (back_debug) console.debug(lasterror);
         }
         if (back_debug) console.debug(result);
         resolve(result);
      } catch (e) {
         reject(e);
      }
   });
};
var TabsSendMessage = function (tabid: number, message: object, options?: any): Promise<any> {
   return new Promise(function (resolve, reject) {
      try {
         chrome.tabs.sendMessage(tabid, message, options, (result) => {
            if (chrome.runtime.lastError) {
               reject(chrome.runtime.lastError.message);
               return;
            }
            resolve(result);
         });
      } catch (e) {
         reject(e);
      }
   });
};
var getAllFrames = function (tabid): Promise<chrome.webNavigation.GetAllFrameResultDetails[]> {
   return new Promise(function (resolve, reject) {
      try {
         chrome.webNavigation.getAllFrames({ tabId: tabid }, (details) => {
            if (chrome.runtime.lastError) {
               reject(chrome.runtime.lastError.message);
               return;
            }
            resolve(details);
         });
      } catch (e) {
         reject(e);
      }
   });
};
var tabscreate = function (createProperties): Promise<chrome.tabs.Tab> {
   return new Promise(function (resolve, reject) {
      try {
         chrome.tabs.create(createProperties, (tab) => {
            if (chrome.runtime.lastError) {
               reject(chrome.runtime.lastError.message);
               return;
            }
            resolve(tab);
         });
      } catch (e) {
         reject(e);
      }
   });
};
OnPageLoad();
chrome.tabs.onCreated.addListener(tabsOnCreated);
chrome.tabs.onRemoved.addListener(tabsOnRemoved);
chrome.tabs.onUpdated.addListener(tabsOnUpdated);
chrome.tabs.onActivated.addListener(tabsOnActivated);
chrome.downloads.onChanged.addListener(downloadsOnChanged);
chrome.runtime.onMessage.addListener((msg, sender, fnResponse) => {
   if (msg === "loadscript") {
      if (openrpautil_script !== null && openrpautil_script !== undefined && openrpautil_script !== '') {
         if (back_debug) console.debug("send openrpautil to tab");
         fnResponse(openrpautil_script);
      } else {
         console.warn("tab requested script, but openrpautil has not been loaded");
         fnResponse(null);
      }
   }
   else {
      bck_runtimeOnMessage(msg, sender, fnResponse);
   }
});
async function bck_runtimeOnMessage(msg, sender, fnResponse) {
   if (port == null) return;
   if (isChrome) msg.browser = "chrome";
   if (isFirefox) msg.browser = "ff";
   if (isChromeEdge) msg.browser = "edge";
   msg.tabid = sender.tab.id;
   msg.windowId = sender.tab.windowId;
   msg.frameId = sender.frameId;
   if (msg.uix && msg.uiy) {
      //var currentWindow = await windowsget(sender.windowId);
      //if (!('id' in currentWindow)) return;
      var allWindows = await windowsgetAll();
      var win = allWindows.filter(x => x.id == sender.tab.windowId);
      var currentWindow = allWindows[0];
      if (win.length > 0) currentWindow = win[0];
      msg.uix += currentWindow.left;
      msg.uiy += currentWindow.top;

      // https://docs.microsoft.com/en-us/dotnet/framework/winforms/controls/how-to-size-a-windows-forms-label-control-to-fit-its-contents
      // if (msg.functionName !== "mousemove") console.debug("[send]" + msg.functionName + " (" + msg.uix + "," + msg.uiy + ")");
      if (msg.functionName !== "mousemove") if (back_debug) console.debug("[send]" + msg.functionName + " (" + msg.uix + "," + msg.uiy + ")");
      if (port != null) port.postMessage(JSON.parse(JSON.stringify(msg)));
   }
   else {
      if (msg.functionName !== "keydown" && msg.functionName !== "keyup") if (back_debug) console.debug("[send]" + msg.functionName);
      if (port != null) port.postMessage(JSON.parse(JSON.stringify(msg)));
   }
}

if (port != null) {
   if (port != null) port.onMessage.addListener(OnPortMessage);
   if (port != null) port.onDisconnect.addListener(OnPortDisconnect);
}
if (openrpautil_script === null || openrpautil_script === undefined || openrpautil_script === '') {
   if (port != null) {
      var message = { functionName: "openrpautilscript" };
      try {
         if (back_debug) console.debug("[send]" + message.functionName);
         if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
      } catch (e) {
         console.error(e);
         port = null;
      }
   }
}
setInterval(function () {
   try {
      if (port != null) {
         var message = { functionName: "ping", browser: "chrome" };
         if (isChrome) message.browser = "chrome";
         if (isFirefox) message.browser = "ff";
         if (isChromeEdge) message.browser = "edge";
         // if(back_debug) console.debug("[send]" + message.functionName);
         if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
      } else {
         if (back_debug) console.debug("no port, cannot ping");
      }
   } catch (e) {
      console.error(e);
   }
}, 1000);


var downloadsSearch = function (id) {
   return new Promise(function (resolve, reject) {
      try {
         chrome.downloads.search({ id: id }, function (data) {
            if (chrome.runtime.lastError) {
               return reject(chrome.runtime.lastError);
            }
            if (data != null && data.length > 0) {
               return resolve(data[0]);
            }
            resolve(null);
         });
      } catch (e) {
         reject(e);
      }
   });
};
async function downloadsOnChanged(delta) {
   if (!delta.state ||
      (delta.state.current != 'complete')) {
      return;
   }
   const download = await downloadsSearch(delta.id);
   if (back_debug) console.debug(download);

   if (port == null) return;
   var message = { functionName: "downloadcomplete", data: JSON.stringify(download), browser: "chrome" };
   if (isChrome) message.browser = "chrome";
   if (isFirefox) message.browser = "ff";
   if (isChromeEdge) message.browser = "edge";
   if (back_debug) console.debug("[send]" + message.functionName);
   if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));

}



var debuggerattach = function (tabId): Promise<void> {
   return new Promise(function (resolve, reject) {
      chrome.debugger.attach({ tabId }, "1.0", () => {
         if (chrome.runtime.lastError) {
            return reject(chrome.runtime.lastError);
         }
         resolve();
      });
   });
}
var debuggerdetach = function (tabId): Promise<void> {
   return new Promise(function (resolve, reject) {
      try {
         chrome.debugger.detach({ tabId }, () => {
            if (chrome.runtime.lastError) {
               return reject(chrome.runtime.lastError);
            }
            resolve();
         });
      } catch (e) {
         return reject(e);
      }
   });
}
var debuggerEvaluate = function (tabId, code) {
   return new Promise(function (resolve, reject) {
      chrome.debugger.sendCommand({ tabId }, "Runtime.evaluate", { expression: code }, (result) => {
         if (chrome.runtime.lastError) {
            return reject(chrome.runtime.lastError);
         }
         resolve(result);
      });
   });
}



var permissionscontains = function (permissions) {
   return new Promise(function (resolve, reject) {
      chrome.permissions.contains(permissions, (result) => {
         if (chrome.runtime.lastError) {
            return reject(chrome.runtime.lastError);
         }
         resolve(result);
      });
   });
}
var permissionsremove = function (permissions) {
   return new Promise(function (resolve, reject) {
      chrome.permissions.remove(permissions, (result) => {
         if (chrome.runtime.lastError) {
            return reject(chrome.runtime.lastError);
         }
         resolve(result);
      });
   });
}
var permissionsrequest = function (permissions) {
   return new Promise(function (resolve, reject) {
      try {
         chrome.permissions.request(permissions, (result) => {
            if (chrome.runtime.lastError) {
               return reject(chrome.runtime.lastError);
            }
            resolve(result);
         });
      } catch (e) {
         return reject(e);
      }
   });
}


var hastabpermission = function (origins) {
   return permissionscontains({
      permissions: ['tabs'],
      origins: [origins]
   });
}
var removetabpermission = function (origins) {
   return permissionsremove({
      permissions: ['tabs'],
      origins: [origins]
   });
}
var requesttabpermission = function (origins) {
   return permissionsrequest({
      permissions: ['tabs'],
      origins: [origins]
   });
}