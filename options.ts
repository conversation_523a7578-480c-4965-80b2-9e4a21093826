// https://developer.chrome.com/docs/extensions/develop/ui/options-page
// Saves options to chrome.storage

var defaultExcludeDomains = ["ogs.google.com", "meet.google.com", "play.google.com", "docs.google.com", "keep.google.com", "chrome.google.com", "support.mozilla.org",
  "codefileclient.danid.dk", "applet.danid.dk", "nemlog-in.mitid.dk", "business.comcast.com" ]
const saveOptions = () => {
    // @ts-ignore
    const excludeDomains = document.getElementById('excludeDomains').value;
  
    chrome.storage.sync.set(
      { excludeDomains: excludeDomains },
      () => {
        // Update status to let user know options were saved.
        const status = document.getElementById('status');
        status.textContent = 'Options saved.';
        setTimeout(() => {
          status.textContent = '';
        }, 750);
      }
    );
  };
  
  // Restores select box and checkbox state using the preferences
  // stored in chrome.storage.
  const restoreOptions = () => {
    chrome.storage.sync.get(
      { excludeDomains: defaultExcludeDomains.join(",") },
      (items) => {
        // @ts-ignore
        document.getElementById('excludeDomains').value = items.excludeDomains;

        // Update status to let user know options were saved.
        const status = document.getElementById('status');
        status.textContent = 'Options loaded.';
        setTimeout(() => {
          status.textContent = '';
        }, 750);
        
      }
    );
  };
  
  document.addEventListener('DOMContentLoaded', restoreOptions);
  document.getElementById('save').addEventListener('click', saveOptions);