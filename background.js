(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
var port = null;
var portname = 'com.openrpa.msg';
var base_debug = false;
var back_debug = false;
chrome.action.onClicked.addListener(function (tab) {
    chrome.runtime.openOptionsPage();
});
function BaseOnPortMessage(message) {
    if (port == null) {
        console.warn("BaseOnPortMessage: port is null!");
        console.debug(message);
        return;
    }
    if (message === null || message === undefined) {
        console.warn("BaseOnPortMessage: Received null message!");
        return;
    }
    if (message.functionName === "ping")
        return;
    if (base_debug)
        console.debug("[baseresc][" + message.messageid + "]" + message.functionName);
    if (message.functionName === "backgroundscript") {
        try {
            message.result = "ok";
        }
        catch (e) {
            console.error(e);
            message.result = e;
        }
        return;
    }
}
;
function BaseOnPortDisconnect(message) {
    if (base_debug)
        console.debug("BaseOnPortDisconnect: " + message);
    port = null;
    if (chrome.runtime.lastError) {
        console.warn("BaseOnPortDisconnect: " + chrome.runtime.lastError.message);
        port = null;
        setTimeout(function () {
            Baseconnect();
        }, 1000);
        return;
    }
    else {
        if (base_debug)
            console.debug("BaseOnPortDisconnect from native port");
    }
}
function Baseconnect() {
    if (base_debug)
        console.debug("Baseconnect()");
    if (port !== null && port !== undefined) {
        try {
            if (port != null)
                port.onMessage.removeListener(BaseOnPortMessage);
            if (port != null)
                port.onDisconnect.removeListener(BaseOnPortDisconnect);
        }
        catch (e) {
            console.debug(e);
        }
    }
    if (port === null || port === undefined) {
        try {
            if (back_debug)
                console.debug("Connecting to " + portname);
            port = chrome.runtime.connectNative(portname);
        }
        catch (e) {
            console.error(e);
            port = null;
            return;
        }
    }
    if (port != null)
        port.onMessage.addListener(BaseOnPortMessage);
    if (port != null)
        port.onDisconnect.addListener(BaseOnPortDisconnect);
    if (chrome.runtime.lastError) {
        console.warn("Whoops.. " + chrome.runtime.lastError.message);
        port = null;
        return;
    }
    else {
        if (base_debug)
            console.debug("Connected to native port, request backgroundscript script");
    }
}
Baseconnect();
if (back_debug)
    console.debug('openrpa extension begin');
var openrpautil_script = '';
var portname = 'com.openrpa.msg';
var lastwindowId = 1;
var openrpadebug = false;
// Firefox 1.0+ (tested on Firefox 45 - 53)
var isFirefox = typeof InstallTrigger !== 'undefined';
// Edge 20+ (tested on Edge 38.14393.0.0)
var isChromeEdge = navigator.appVersion.indexOf('Edge') > -1;
if (!isChromeEdge)
    isChromeEdge = navigator.appVersion.indexOf('Edg') > -1;
var isChrome = !isChromeEdge && !isFirefox;
var portname = 'com.openrpa.msg';
async function SendToTab(windowId, message) {
    try {
        var retry = false;
        try {
            if (back_debug)
                console.debug("SendToTab: send message to tab id " + message.tabid + " windowId " + windowId);
            message = await tabssendMessage(message.tabid, message);
        }
        catch (e) {
            if (back_debug)
                console.debug('tabssendMessage failed once');
            retry = true;
            console.warn(e);
        }
        if (retry) {
            await new Promise(r => setTimeout(r, 2000));
            if (back_debug)
                console.debug("retry: SendToTab: send message to tab id " + message.tabid + " windowId " + windowId);
            message = await tabssendMessage(message.tabid, message);
        }
        var allWindows = await windowsgetAll();
        var win = allWindows.filter(x => x.id == windowId);
        var currentWindow = allWindows[0];
        if (win.length > 0)
            currentWindow = win[0];
        if (message.uix && message.uiy) {
            message.uix += currentWindow.left;
            message.uiy += currentWindow.top;
        }
        if (message.results) {
            if (Array.isArray(message.results)) {
                message.results.forEach((item) => {
                    if (item.uix && item.uiy) {
                        item.uix += currentWindow.left;
                        item.uiy += currentWindow.top;
                    }
                    item.windowId = windowId;
                    item.tabid = message.tabid;
                });
            }
            else {
                delete message.results;
            }
        }
    }
    catch (e) {
        console.error(e);
        message.error = JSON.stringify(e);
    }
    return message;
}
async function OnPortMessage(message) {
    try {
        if (port == null) {
            console.warn("OnPortMessage: port is null!", message);
            return;
        }
        if (message === null || message === undefined) {
            console.warn("OnPortMessage: Received null message!");
            return;
        }
        if (message.functionName === "ping") {
            return;
        }
        if (isChrome)
            message.browser = "chrome";
        if (isFirefox)
            message.browser = "ff";
        if (isChromeEdge)
            message.browser = "edge";
        if (back_debug)
            console.debug("[resc][" + message.messageid + "]" + message.functionName);
        if (message.functionName === "openrpautilscript") {
            return;
        }
        if (message.functionName === "contentscript") {
            return;
        }
        if (message.functionName === "enumwindows") {
            await EnumWindows(message);
            if (back_debug)
                console.debug("[send][" + message.messageid + "]" + message.functionName + " results: " + message.results.length);
            if (port != null)
                port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
        }
        if (message.functionName === "enumtabs") {
            await EnumTabs(message);
            var _result = "";
            message.results = message.results.filter(x => x.type != "popup" && x.state != "minimized");
            for (var i = 0; i < message.results.length; i++) {
                var _tab = message.results[i].tab;
                _result += "(tabid " + _tab.id + "/index:" + _tab.index + ") ";
            }
            if (back_debug)
                console.debug("[send][" + message.messageid + "]" + message.functionName + " results: " + message.results.length + " " + _result);
            if (port != null)
                port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
        }
        if (message.functionName === "selecttab") {
            let tab = null;
            var tabsList = await tabsquery();
            if (tabsList.length == 0) {
                message.error = "selecttab, No tabs found!";
                if (port != null)
                    port.postMessage(JSON.parse(JSON.stringify(message)));
                return;
            }
            for (var i = 0; i < tabsList.length; i++) {
                if (tabsList[i].id == message.tabid) {
                    tab = tabsList[i];
                }
            }
            if (tab == null) {
                message.error = "Tab " + message.tabid + " not found!";
                if (port != null)
                    port.postMessage(JSON.parse(JSON.stringify(message)));
                return;
            }
            await tabshighlight(tab.index);
            message.tab = tab;
            // message.tab = await tabsupdate(message.tabid, { highlighted: true });
            if (back_debug)
                console.debug("[send][" + message.messageid + "]" + message.functionName + " " + message.tab.url);
            if (port != null)
                port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
        }
        if (message.functionName === "updatetab") {
            var tabsList = await tabsquery();
            if (tabsList.length == 0) {
                message.error = "updatetab, No tabs found!";
                if (port != null)
                    port.postMessage(JSON.parse(JSON.stringify(message)));
                return;
            }
            for (var i = 0; i < tabsList.length; i++) {
                if (tabsList[i].id == message.tabid) {
                    if (tabsList[i].url == message.tab.url) {
                        delete message.tab.url;
                    }
                }
            }
            delete message.tab.browser;
            delete message.tab.audible;
            delete message.tab.discarded;
            delete message.tab.favIconUrl;
            delete message.tab.height;
            delete message.tab.id;
            delete message.tab.incognito;
            delete message.tab.status;
            delete message.tab.title;
            delete message.tab.width;
            delete message.tab.index;
            delete message.tab.windowId;
            message.tab = await tabsupdate(message.tabid, message.tab);
            if (back_debug)
                console.debug("[send][" + message.messageid + "]" + message.functionName + " " + message.tab.url);
            if (port != null)
                port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
        }
        if (message.functionName === "closetab") {
            chrome.tabs.remove(message.tabid);
            if (port != null)
                port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
        }
        if (message.functionName === "openurl") {
            var windows = await windowsgetAll();
            if (back_debug)
                console.debug("openurl windowid" + message.windowId + " tabid: " + message.tabid, message);
            if (back_debug)
                console.debug("windows", windows);
            if (message.xPath == "true") {
                let createProperties = { url: message.data, windowId: undefined };
                var w = await getCurrentWindow();
                if (back_debug)
                    console.debug("WINDOW", w);
                createProperties.windowId = w.id;
                if (back_debug)
                    console.debug("openurl, open NEW in window " + w.id);
                //if (message.windowId !== null && message.windowId !== undefined && message.windowId > 0) createProperties.windowId = message.windowId;
                var newtab = await tabscreate(createProperties);
                message.tab = newtab;
                message.tabid = newtab.id;
                if (back_debug)
                    console.debug("[send][" + message.messageid + "]" + message.functionName);
                if (port != null)
                    port.postMessage(JSON.parse(JSON.stringify(message)));
                return;
            }
            if (message.windowId == -1) {
                var w = await getCurrentWindow();
                message.windowId = w.id;
            }
            else {
                windows = windows.filter(x => x.type != "popup" && x.state != "minimized");
                var win = windows.filter(x => x.id == message.windowId);
                if (win.length == 0) {
                    var w = await getCurrentWindow();
                    message.windowId = w.id;
                }
            }
            var tabsList = await tabsquery({ windowId: message.windowId });
            if (message.windowId !== null && message.windowId !== undefined && message.windowId !== '' && message.windowId > 0) {
                tabsList = tabsList.filter(x => x.windowId == message.windowId);
            }
            else {
                tabsList = tabsList.filter(x => x.windowId == lastwindowId);
            }
            if (message.tabid !== null && message.tabid !== undefined && message.tabid !== '' && message.tabid > 0) {
                tabsList = tabsList.filter(x => x.id == message.tabid);
            }
            else {
                tabsList = tabsList.filter(x => x.active == true);
            }
            if (tabsList.length == 0) {
                var w = await getCurrentWindow();
                // tabsList = await tabsquery({ windowId: w.id });
                let createProperties = { url: message.data, windowId: -1 };
                createProperties.windowId = w.id;
                if (back_debug)
                    console.debug("openurl, open NEW 2 in window " + w.id);
                var newtab = await tabscreate(createProperties);
                if (back_debug)
                    console.debug(newtab);
                message.tab = newtab;
                message.tabid = newtab.id;
                if (back_debug)
                    console.debug("[send][" + message.messageid + "]" + message.functionName);
                if (port != null)
                    port.postMessage(JSON.parse(JSON.stringify(message)));
                return;
                // message.error = "openurl, No tabs found!";
                // if(port!=null) port.postMessage(JSON.parse(JSON.stringify(message)));
                // return;
            }
            let tab = tabsList[0];
            if (tab.url != message.data) {
                let updateoptions = { active: true, highlighted: true, url: "" };
                updateoptions.url = message.data;
                if (back_debug)
                    console.debug("openurl, update tab #" + tab.id, tab);
                lastwindowId = tab.windowId;
                tab = await tabsupdate(tab.id, updateoptions);
            }
            else {
                let updateoptions = { active: true, highlighted: true, url: "" };
                updateoptions.url = message.data;
                if (back_debug)
                    console.debug("openurl, tab #" + tab.id + " windowId " + tab.windowId + " is already on the right url", tab);
                lastwindowId = tab.windowId;
                tab = await tabsupdate(tab.id, updateoptions);
            }
            message.tab = tab;
            message.tabid = tab.id;
            if (back_debug)
                console.debug("[send][" + message.messageid + "]" + message.functionName);
            if (port != null)
                port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
        }
        if (back_debug)
            console.debug("SendToTab before tab #" + message.tabid + " windowId " + message.windowId, tab);
        var windowId = lastwindowId;
        var tabsList = await tabsquery();
        if (message.windowId !== null && message.windowId !== undefined && message.windowId !== '' && message.windowId > 0) {
            windowId = message.windowId;
            tabsList = tabsList.filter(x => x.windowId == message.windowId);
        }
        else {
            var templist = tabsList.filter(x => x.windowId == lastwindowId);
            if (templist.length > 0) {
                windowId = lastwindowId;
                tabsList = tabsList.filter(x => x.windowId == lastwindowId);
            }
        }
        if (message.tabid !== null && message.tabid !== undefined && message.tabid !== '' && message.tabid > 0) {
            tabsList = tabsList.filter(x => x.id == message.tabid);
        }
        else {
            tabsList = tabsList.filter(x => x.active == true);
        }
        if (tabsList.length == 0) {
            var w = await getCurrentWindow();
            tabsList = await tabsquery({ windowId: w.id });
            if (tabsList.length == 0) {
                message.error = "SendToTab, No tabs found! windowId " + message.windowId + " lastwindowId: " + lastwindowId;
                if (back_debug)
                    console.debug("[send][" + message.messageid + "]" + message.functionName + " No tabs found");
                if (port != null)
                    port.postMessage(JSON.parse(JSON.stringify(message)));
                return;
            }
            windowId = w.id;
        }
        var tab = tabsList[0];
        if (back_debug)
            console.debug("SendToTab tab #" + tab.id + " windowid " + tab.windowId, tab);
        message.windowId = windowId;
        message.tabid = tab.id;
        openrpadebug = message.debug;
        if (message.functionName === "executescript") {
            if (back_debug)
                console.debug("executescript tab #" + message.tabid + " frameId: " + message.frameId, message);
            // var script = "(" + message.script + ")()";
            var script = message.script;
            var detatch = false;
            try {
                if (back_debug)
                    console.debug("attach to " + message.tabid);
                await debuggerattach(message.tabid);
                detatch = true;
                if (back_debug)
                    console.debug("eval", message.script);
                message.result = await debuggerEvaluate(message.tabid, script);
                if (message.result && message.result.result && message.result.result.value) {
                    message.result = [message.result.result.value];
                }
                else if (!Array.isArray(message.result)) {
                    message.result = [message.result];
                }
                if (back_debug)
                    console.debug("result", message.result);
                // message.results = [message.result];
            }
            catch (e) {
                console.error(e);
                message.error = e.message;
            }
            if (detatch) {
                try {
                    if (back_debug)
                        console.debug("detach to " + message.tabid);
                    await debuggerdetach(message.tabid);
                }
                catch (e) {
                    console.error(e);
                }
            }
            if (back_debug)
                console.debug("[send][" + message.messageid + "]" + message.functionName);
            port.postMessage(JSON.parse(JSON.stringify(message)));
            return;
        }
        //if (message.functionName === "executescript") {
        //    if(back_debug) console.debug("executescript tab #" + message.tabid + " frameId: " + message.frameId, message);
        //    var detatch = false;
        //    try {
        //        if(back_debug) console.debug("attach to " + message.tabid);
        //        await debuggerattach(message.tabid);
        //        detatch = true;
        //        if(back_debug) console.debug("eval", message.script);
        //        message.result = await debuggerEvaluate(message.tabid, message.script);
        //        if (message.result && message.result.result && message.result.result.value) {
        //            message.result = message.result.result.value;
        //        }
        //        if(back_debug) console.debug("result", message.result);
        //        message.results = [message.result];
        //    } catch (e) {
        //        console.error(e);
        //        message.error = e.message;
        //    }
        //    if (detatch) {
        //        try {
        //            if(back_debug) console.debug("detach to " + message.tabid);
        //            await debuggerdetach(message.tabid);
        //        } catch (e) {
        //            console.error(e);
        //        }
        //    }
        //    if(back_debug) console.debug("[send][" + message.messageid + "]" + message.functionName);
        //    if (port != null) port.postMessage(JSON.parse(JSON.stringify(message)));
        //    return;
        //}
        message = await SendToTab(windowId, message);
    }
    catch (e) {
        console.error(e);
        message.error = JSON.stringify(e);
    }
    if (back_debug)
        console.debug("[send][" + message.messageid + "]" + message.functionName);
    if (port != null)
        port.postMessage(JSON.parse(JSON.stringify(message)));
    if (back_debug)
        console.debug(message);
}
;
function OnPortDisconnect(message) {
    if (back_debug)
        console.debug("OnPortDisconnect", message);
    port = null;
    if (chrome.runtime.lastError) {
        console.warn("onDisconnect: " + chrome.runtime.lastError.message);
        port = null;
        setTimeout(function () {
            connect();
        }, 1000);
        return;
    }
    else {
        if (back_debug)
            console.debug("onDisconnect from native port");
    }
}
function connect() {
    if (back_debug)
        console.debug("connect()");
    if (port !== null && port !== undefined) {
        try {
            if (port != null)
                port.onMessage.removeListener(OnPortMessage);
            if (port != null)
                port.onDisconnect.removeListener(OnPortDisconnect);
        }
        catch (e) {
            console.error(e);
        }
    }
    if (port === null || port === undefined) {
        try {
            if (back_debug)
                console.debug("Connecting to " + portname);
            port = chrome.runtime.connectNative(portname);
        }
        catch (e) {
            console.error(e);
            port = null;
            return;
        }
    }
    if (port != null)
        port.onMessage.addListener(OnPortMessage);
    if (port != null)
        port.onDisconnect.addListener(OnPortDisconnect);
    if (chrome.runtime.lastError) {
        console.warn("Whoops.. " + chrome.runtime.lastError.message);
        port = null;
        return;
    }
    else {
        if (back_debug)
            console.debug("Connected to native port");
    }
}
async function EnumTabs(message) {
    if (isChrome)
        message.browser = "chrome";
    if (isFirefox)
        message.browser = "ff";
    if (isChromeEdge)
        message.browser = "edge";
    message.results = [];
    var tabsList = await tabsquery();
    tabsList.forEach((tab) => {
        var _message = { functionName: "tabcreated", tabid: tab.id, tab: tab, browser: "chrome" };
        if (isChrome)
            _message.browser = "chrome";
        if (isFirefox)
            _message.browser = "ff";
        if (isChromeEdge)
            _message.browser = "edge";
        message.results.push(_message);
    });
}
async function EnumWindows(message) {
    var allWindows = await windowsgetAll();
    message.results = [];
    for (var i in allWindows) {
        var window = allWindows[i];
        var _message = { functionName: "windowcreated", windowId: window.id, result: JSON.stringify(window), browser: "chrome" };
        if (isChrome)
            _message.browser = "chrome";
        if (isFirefox)
            _message.browser = "ff";
        if (isChromeEdge)
            _message.browser = "edge";
        message.results.push(_message);
    }
}
async function OnPageLoad() {
    // if (window) window.removeEventListener("load", OnPageLoad, false);
    chrome.windows.onCreated.addListener((window) => {
        if (window.type === "normal" || window.type === "popup") { // panel
            if (window.type === "popup" && window.state == "minimized")
                return;
            if (window.id > 0)
                lastwindowId = window.id;
            var message = { functionName: "windowcreated", windowId: window.id, result: JSON.stringify(window), browser: "chrome" };
            if (isChrome)
                message.browser = "chrome";
            if (isFirefox)
                message.browser = "ff";
            if (isChromeEdge)
                message.browser = "edge";
            if (back_debug)
                console.debug("[send]" + message.functionName + " " + window.id);
            if (port != null)
                port.postMessage(JSON.parse(JSON.stringify(message)));
        }
    });
    chrome.windows.onRemoved.addListener((windowId) => {
        var message = { functionName: "windowremoved", windowId: windowId, browser: "chrome" };
        if (isChrome)
            message.browser = "chrome";
        if (isFirefox)
            message.browser = "ff";
        if (isChromeEdge)
            message.browser = "edge";
        if (back_debug)
            console.debug("[send]" + message.functionName + " " + windowId);
        if (port != null)
            port.postMessage(JSON.parse(JSON.stringify(message)));
    });
    chrome.windows.onFocusChanged.addListener((windowId) => {
        var message = { functionName: "windowfocus", windowId: windowId, browser: "chrome" };
        if (windowId > 0)
            lastwindowId = windowId;
        if (isChrome)
            message.browser = "chrome";
        if (isFirefox)
            message.browser = "ff";
        if (isChromeEdge)
            message.browser = "edge";
        if (back_debug)
            console.debug("[send]" + message.functionName + " " + windowId);
        if (port != null)
            port.postMessage(JSON.parse(JSON.stringify(message)));
    });
    if (port == null)
        return;
}
async function tabsOnCreated(tab) {
    if (port == null)
        return;
    var message = { functionName: "tabcreated", tab: tab, tabid: tab.id, browser: "chrome" };
    if (isChrome)
        message.browser = "chrome";
    if (isFirefox)
        message.browser = "ff";
    if (isChromeEdge)
        message.browser = "edge";
    if (back_debug)
        console.debug("[send]" + message.functionName);
    if (port != null)
        port.postMessage(JSON.parse(JSON.stringify(message)));
}
function tabsOnRemoved(tabId) {
    if (port == null)
        return;
    var message = { functionName: "tabremoved", tabid: tabId, browser: "chrome" };
    if (isChrome)
        message.browser = "chrome";
    if (isFirefox)
        message.browser = "ff";
    if (isChromeEdge)
        message.browser = "edge";
    if (back_debug)
        console.debug("[send]" + message.functionName);
    if (port != null)
        port.postMessage(JSON.parse(JSON.stringify(message)));
}
async function tabsOnUpdated(tabId, changeInfo, tab) {
    if (!allowExecuteScript(tab)) {
        if (back_debug)
            console.debug('tabsOnUpdated, skipped, not allowExecuteScript');
        return;
    }
    if (openrpadebug)
        console.debug(changeInfo);
    try {
        if (back_debug)
            console.debug("tabsOnUpdated: " + changeInfo.status);
        if (openrpautil_script != null) {
            // tabsexecuteScript(tab.id, { code: openrpautil_script, allFrames: true });
        }
    }
    catch (e) {
        //    console.debug(e);
        //    if(back_debug) console.debug(tab);
        return;
    }
    var message = { functionName: "tabupdated", tabid: tabId, tab: tab, browser: "chrome" };
    if (isChrome)
        message.browser = "chrome";
    if (isFirefox)
        message.browser = "ff";
    if (isChromeEdge)
        message.browser = "edge";
    if (back_debug)
        console.debug("[send]" + message.functionName);
    if (port != null)
        port.postMessage(JSON.parse(JSON.stringify(message)));
}
function tabsOnActivated(activeInfo) {
    if (port == null)
        return;
    var message = { functionName: "tabactivated", tabid: activeInfo.tabId, windowId: activeInfo.windowId, browser: "chrome" };
    if (isChrome)
        message.browser = "chrome";
    if (isFirefox)
        message.browser = "ff";
    if (isChromeEdge)
        message.browser = "edge";
    if (back_debug)
        console.debug("[send]" + message.functionName);
    if (port != null)
        port.postMessage(JSON.parse(JSON.stringify(message)));
}
var allowExecuteScript = function (tab) {
    if (port == null)
        return false;
    if (tab.url == null)
        return false;
    if (isFirefox) {
        if (tab.url.startsWith("about:"))
            return false;
        if (tab.url.startsWith("https://support.mozilla.org"))
            return false;
    }
    // ff uses chrome:// when debugging ?
    if (tab.url.startsWith("chrome://"))
        return false;
    if (isChrome) {
        if (tab.url.startsWith("https://chrome.google.com"))
            return false;
    }
    if (tab.url.startsWith("https://docs.google.com/spreadsheets/d")) {
        if (back_debug)
            console.debug("skip google docs");
        return false;
    }
    return true;
};
var getCurrentWindow = function () {
    return new Promise(function (resolve, reject) {
        chrome.windows.getCurrent(async (curWindow) => {
            if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError.message);
                return;
            }
            if (curWindow.type == "popup" && curWindow.state == "minimized") {
                if (back_debug)
                    console.debug("Current window #" + curWindow.id + " looks like a background page, look for another window");
                var windows = await windowsgetAll();
                windows = windows.filter(x => x.type != "popup" && x.state != "minimized");
                if (windows.length > 0)
                    return resolve(windows[0]);
            }
            resolve(curWindow);
        });
    });
};
var tabsquery = function (options) {
    return new Promise(function (resolve, reject) {
        try {
            if (options === null || options === undefined)
                options = {};
            chrome.tabs.query(options, async (tabsList) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError.message);
                    return;
                }
                return resolve(tabsList);
                // fix: https://stackoverflow.com/questions/59974414/chrome-tabs-query-returning-empty-tab-array
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
var windowsgetAll = function () {
    return new Promise(function (resolve, reject) {
        try {
            chrome.windows.getAll({ populate: false }, (allWindows) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError.message);
                    return;
                }
                resolve(allWindows);
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
// @ts-ignore
var tabsexecuteScript = function (tabid, options) {
    return new Promise(function (resolve, reject) {
        try {
            // https://blog.bitsrc.io/what-is-chrome-scripting-api-f8dbdb6e3987
            var opt = Object.assign(options, { target: { tabId: tabid, allFrames: true } });
            if (opt.code) {
                opt.func = eval(opt.code);
                delete opt.code;
            }
            chrome.scripting.executeScript(opt, function (results) {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError.message);
                    return;
                }
                resolve(results);
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
var getCurrentTab = function () {
    return new Promise(function (resolve, reject) {
        try {
            chrome.tabs.getCurrent((tab) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError.message);
                    return;
                }
                resolve(tab);
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
var tabsupdate = function (tabid, updateoptions) {
    return new Promise(function (resolve, reject) {
        try {
            chrome.tabs.update(tabid, updateoptions, (tab) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError.message);
                    return;
                }
                resolve(tab);
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
var tabshighlight = function (index) {
    return new Promise(function (resolve, reject) {
        try {
            chrome.tabs.highlight({ 'tabs': index, windowId: lastwindowId }, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError.message);
                    return;
                }
                resolve();
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
var tabssendMessage = function (tabid, message) {
    return new Promise(async function (resolve, reject) {
        try {
            var details = [];
            if (message.frameId == -1) {
                details = await getAllFrames(tabid);
                if (back_debug)
                    console.debug("tabssendMessage, found " + details.length + " frames in tab " + tabid);
            }
            else {
                if (back_debug)
                    console.debug("tabssendMessage, sending to tab " + tabid + " frameid " + message.frameId);
            }
            var result = null;
            var lasterror = "tabssendMessage: No error";
            if (details.length > 1) {
                for (var i = 0; i < details.length; i++) {
                    try {
                        var frame = details[i];
                        if (!allowExecuteScript(frame))
                            continue;
                        var tmp = await TabsSendMessage(tabid, message, { frameId: frame.frameId });
                        if (result == null) {
                            result = tmp;
                            result.frameId = frame.frameId;
                            if (result.result != null) {
                                result.frameId = frame.frameId;
                            }
                            if (result.results != null && result.results.length > 0) {
                                for (var z = 0; z < result.results.length; z++) {
                                    result.results[z].frameId = frame.frameId;
                                }
                            }
                        }
                        else {
                            if (result.result != null || result.result != undefined) {
                                //if (typeof result.result == "string") {
                                //    result.results = [JSON.parse(result.result)];
                                //} else {
                                //    result.results = [result.result];
                                //}                            
                                //delete result.result;
                            }
                            if (tmp.result != null) {
                                tmp.result.frameId = frame.frameId;
                                if (result.results == null)
                                    result.results = [];
                                result.results.push(tmp.result);
                            }
                            if (tmp.results != null && tmp.results.length > 0) {
                                for (var z = 0; z < tmp.results.length; z++) {
                                    tmp.results[z].frameId = frame.frameId;
                                }
                                result.results = result.results.concat(tmp.results);
                            }
                        }
                    }
                    catch (e) {
                        lasterror = e;
                        console.debug(e);
                    }
                }
            }
            if (details.length == 0 || details.length == 1) {
                lasterror = null;
                try {
                    if (message.frameId > -1)
                        result = await TabsSendMessage(tabid, message, { frameId: message.frameId });
                    if (message.frameId == -1)
                        result = await TabsSendMessage(tabid, message);
                }
                catch (e) {
                    lasterror = e;
                }
                if (result == null) {
                    try {
                        if (back_debug)
                            console.debug("result == null, so send to tab id #" + tabid);
                        result = await TabsSendMessage(tabid, message);
                        if (result != null)
                            lasterror = null;
                    }
                    catch (e) {
                        if (lasterror == null)
                            lasterror = e;
                    }
                }
                if (result == null) {
                    var w = await getCurrentWindow();
                    let tabsList = await tabsquery({ windowId: w.id });
                    if (tabsList.length > 0) {
                        var tab = tabsList[0];
                        if (back_debug)
                            console.debug("result == null, still null default window is #" + w.id + " and first tab is #" + tab.id + " so send to that!");
                        if (back_debug)
                            console.debug("window", w);
                        if (back_debug)
                            console.debug("tabsList", tabsList);
                        result = await TabsSendMessage(tab.id, message);
                    }
                }
            }
            if (result == null || result == undefined) {
                // this will fail with "Could not establish connection. Receiving end does not exist." when page is loading, so just send empty result to robot, it will try again
                //if(back_debug) console.debug("tabssendMessage has no result, return original message");
                //message.error = lasterror;
                result = message;
                if (back_debug)
                    console.debug(lasterror);
            }
            if (back_debug)
                console.debug(result);
            resolve(result);
        }
        catch (e) {
            reject(e);
        }
    });
};
var TabsSendMessage = function (tabid, message, options) {
    return new Promise(function (resolve, reject) {
        try {
            chrome.tabs.sendMessage(tabid, message, options, (result) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError.message);
                    return;
                }
                resolve(result);
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
var getAllFrames = function (tabid) {
    return new Promise(function (resolve, reject) {
        try {
            chrome.webNavigation.getAllFrames({ tabId: tabid }, (details) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError.message);
                    return;
                }
                resolve(details);
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
var tabscreate = function (createProperties) {
    return new Promise(function (resolve, reject) {
        try {
            chrome.tabs.create(createProperties, (tab) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError.message);
                    return;
                }
                resolve(tab);
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
OnPageLoad();
chrome.tabs.onCreated.addListener(tabsOnCreated);
chrome.tabs.onRemoved.addListener(tabsOnRemoved);
chrome.tabs.onUpdated.addListener(tabsOnUpdated);
chrome.tabs.onActivated.addListener(tabsOnActivated);
chrome.downloads.onChanged.addListener(downloadsOnChanged);
chrome.runtime.onMessage.addListener((msg, sender, fnResponse) => {
    if (msg === "loadscript") {
        if (openrpautil_script !== null && openrpautil_script !== undefined && openrpautil_script !== '') {
            if (back_debug)
                console.debug("send openrpautil to tab");
            fnResponse(openrpautil_script);
        }
        else {
            console.warn("tab requested script, but openrpautil has not been loaded");
            fnResponse(null);
        }
    }
    else {
        bck_runtimeOnMessage(msg, sender, fnResponse);
    }
});
async function bck_runtimeOnMessage(msg, sender, fnResponse) {
    if (port == null)
        return;
    if (isChrome)
        msg.browser = "chrome";
    if (isFirefox)
        msg.browser = "ff";
    if (isChromeEdge)
        msg.browser = "edge";
    msg.tabid = sender.tab.id;
    msg.windowId = sender.tab.windowId;
    msg.frameId = sender.frameId;
    if (msg.uix && msg.uiy) {
        //var currentWindow = await windowsget(sender.windowId);
        //if (!('id' in currentWindow)) return;
        var allWindows = await windowsgetAll();
        var win = allWindows.filter(x => x.id == sender.tab.windowId);
        var currentWindow = allWindows[0];
        if (win.length > 0)
            currentWindow = win[0];
        msg.uix += currentWindow.left;
        msg.uiy += currentWindow.top;
        // https://docs.microsoft.com/en-us/dotnet/framework/winforms/controls/how-to-size-a-windows-forms-label-control-to-fit-its-contents
        // if (msg.functionName !== "mousemove") console.debug("[send]" + msg.functionName + " (" + msg.uix + "," + msg.uiy + ")");
        if (msg.functionName !== "mousemove")
            if (back_debug)
                console.debug("[send]" + msg.functionName + " (" + msg.uix + "," + msg.uiy + ")");
        if (port != null)
            port.postMessage(JSON.parse(JSON.stringify(msg)));
    }
    else {
        if (msg.functionName !== "keydown" && msg.functionName !== "keyup")
            if (back_debug)
                console.debug("[send]" + msg.functionName);
        if (port != null)
            port.postMessage(JSON.parse(JSON.stringify(msg)));
    }
}
if (port != null) {
    if (port != null)
        port.onMessage.addListener(OnPortMessage);
    if (port != null)
        port.onDisconnect.addListener(OnPortDisconnect);
}
if (openrpautil_script === null || openrpautil_script === undefined || openrpautil_script === '') {
    if (port != null) {
        var message = { functionName: "openrpautilscript" };
        try {
            if (back_debug)
                console.debug("[send]" + message.functionName);
            if (port != null)
                port.postMessage(JSON.parse(JSON.stringify(message)));
        }
        catch (e) {
            console.error(e);
            port = null;
        }
    }
}
setInterval(function () {
    try {
        if (port != null) {
            var message = { functionName: "ping", browser: "chrome" };
            if (isChrome)
                message.browser = "chrome";
            if (isFirefox)
                message.browser = "ff";
            if (isChromeEdge)
                message.browser = "edge";
            // if(back_debug) console.debug("[send]" + message.functionName);
            if (port != null)
                port.postMessage(JSON.parse(JSON.stringify(message)));
        }
        else {
            if (back_debug)
                console.debug("no port, cannot ping");
        }
    }
    catch (e) {
        console.error(e);
    }
}, 1000);
var downloadsSearch = function (id) {
    return new Promise(function (resolve, reject) {
        try {
            chrome.downloads.search({ id: id }, function (data) {
                if (chrome.runtime.lastError) {
                    return reject(chrome.runtime.lastError);
                }
                if (data != null && data.length > 0) {
                    return resolve(data[0]);
                }
                resolve(null);
            });
        }
        catch (e) {
            reject(e);
        }
    });
};
async function downloadsOnChanged(delta) {
    if (!delta.state ||
        (delta.state.current != 'complete')) {
        return;
    }
    const download = await downloadsSearch(delta.id);
    if (back_debug)
        console.debug(download);
    if (port == null)
        return;
    var message = { functionName: "downloadcomplete", data: JSON.stringify(download), browser: "chrome" };
    if (isChrome)
        message.browser = "chrome";
    if (isFirefox)
        message.browser = "ff";
    if (isChromeEdge)
        message.browser = "edge";
    if (back_debug)
        console.debug("[send]" + message.functionName);
    if (port != null)
        port.postMessage(JSON.parse(JSON.stringify(message)));
}
var debuggerattach = function (tabId) {
    return new Promise(function (resolve, reject) {
        chrome.debugger.attach({ tabId }, "1.0", () => {
            if (chrome.runtime.lastError) {
                return reject(chrome.runtime.lastError);
            }
            resolve();
        });
    });
};
var debuggerdetach = function (tabId) {
    return new Promise(function (resolve, reject) {
        try {
            chrome.debugger.detach({ tabId }, () => {
                if (chrome.runtime.lastError) {
                    return reject(chrome.runtime.lastError);
                }
                resolve();
            });
        }
        catch (e) {
            return reject(e);
        }
    });
};
var debuggerEvaluate = function (tabId, code) {
    return new Promise(function (resolve, reject) {
        chrome.debugger.sendCommand({ tabId }, "Runtime.evaluate", { expression: code }, (result) => {
            if (chrome.runtime.lastError) {
                return reject(chrome.runtime.lastError);
            }
            resolve(result);
        });
    });
};
var permissionscontains = function (permissions) {
    return new Promise(function (resolve, reject) {
        chrome.permissions.contains(permissions, (result) => {
            if (chrome.runtime.lastError) {
                return reject(chrome.runtime.lastError);
            }
            resolve(result);
        });
    });
};
var permissionsremove = function (permissions) {
    return new Promise(function (resolve, reject) {
        chrome.permissions.remove(permissions, (result) => {
            if (chrome.runtime.lastError) {
                return reject(chrome.runtime.lastError);
            }
            resolve(result);
        });
    });
};
var permissionsrequest = function (permissions) {
    return new Promise(function (resolve, reject) {
        try {
            chrome.permissions.request(permissions, (result) => {
                if (chrome.runtime.lastError) {
                    return reject(chrome.runtime.lastError);
                }
                resolve(result);
            });
        }
        catch (e) {
            return reject(e);
        }
    });
};
var hastabpermission = function (origins) {
    return permissionscontains({
        permissions: ['tabs'],
        origins: [origins]
    });
};
var removetabpermission = function (origins) {
    return permissionsremove({
        permissions: ['tabs'],
        origins: [origins]
    });
};
var requesttabpermission = function (origins) {
    return permissionsrequest({
        permissions: ['tabs'],
        origins: [origins]
    });
};

},{}]},{},[1])
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
