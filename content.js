(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
'use strict'

exports.byteLength = byteLength
exports.toByteArray = toByteArray
exports.fromByteArray = fromByteArray

var lookup = []
var revLookup = []
var Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array

var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
for (var i = 0, len = code.length; i < len; ++i) {
  lookup[i] = code[i]
  revLookup[code.charCodeAt(i)] = i
}

// Support decoding URL-safe base64 strings, as Node.js does.
// See: https://en.wikipedia.org/wiki/Base64#URL_applications
revLookup['-'.charCodeAt(0)] = 62
revLookup['_'.charCodeAt(0)] = 63

function getLens (b64) {
  var len = b64.length

  if (len % 4 > 0) {
    throw new Error('Invalid string. Length must be a multiple of 4')
  }

  // Trim off extra bytes after placeholder bytes are found
  // See: https://github.com/beatgammit/base64-js/issues/42
  var validLen = b64.indexOf('=')
  if (validLen === -1) validLen = len

  var placeHoldersLen = validLen === len
    ? 0
    : 4 - (validLen % 4)

  return [validLen, placeHoldersLen]
}

// base64 is 4/3 + up to two characters of the original data
function byteLength (b64) {
  var lens = getLens(b64)
  var validLen = lens[0]
  var placeHoldersLen = lens[1]
  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
}

function _byteLength (b64, validLen, placeHoldersLen) {
  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
}

function toByteArray (b64) {
  var tmp
  var lens = getLens(b64)
  var validLen = lens[0]
  var placeHoldersLen = lens[1]

  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))

  var curByte = 0

  // if there are placeholders, only get up to the last complete 4 chars
  var len = placeHoldersLen > 0
    ? validLen - 4
    : validLen

  var i
  for (i = 0; i < len; i += 4) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 18) |
      (revLookup[b64.charCodeAt(i + 1)] << 12) |
      (revLookup[b64.charCodeAt(i + 2)] << 6) |
      revLookup[b64.charCodeAt(i + 3)]
    arr[curByte++] = (tmp >> 16) & 0xFF
    arr[curByte++] = (tmp >> 8) & 0xFF
    arr[curByte++] = tmp & 0xFF
  }

  if (placeHoldersLen === 2) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 2) |
      (revLookup[b64.charCodeAt(i + 1)] >> 4)
    arr[curByte++] = tmp & 0xFF
  }

  if (placeHoldersLen === 1) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 10) |
      (revLookup[b64.charCodeAt(i + 1)] << 4) |
      (revLookup[b64.charCodeAt(i + 2)] >> 2)
    arr[curByte++] = (tmp >> 8) & 0xFF
    arr[curByte++] = tmp & 0xFF
  }

  return arr
}

function tripletToBase64 (num) {
  return lookup[num >> 18 & 0x3F] +
    lookup[num >> 12 & 0x3F] +
    lookup[num >> 6 & 0x3F] +
    lookup[num & 0x3F]
}

function encodeChunk (uint8, start, end) {
  var tmp
  var output = []
  for (var i = start; i < end; i += 3) {
    tmp =
      ((uint8[i] << 16) & 0xFF0000) +
      ((uint8[i + 1] << 8) & 0xFF00) +
      (uint8[i + 2] & 0xFF)
    output.push(tripletToBase64(tmp))
  }
  return output.join('')
}

function fromByteArray (uint8) {
  var tmp
  var len = uint8.length
  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes
  var parts = []
  var maxChunkLength = 16383 // must be multiple of 3

  // go through the array every three bytes, we'll deal with trailing stuff later
  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {
    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))
  }

  // pad the end with zeros, but make sure to not forget the extra bytes
  if (extraBytes === 1) {
    tmp = uint8[len - 1]
    parts.push(
      lookup[tmp >> 2] +
      lookup[(tmp << 4) & 0x3F] +
      '=='
    )
  } else if (extraBytes === 2) {
    tmp = (uint8[len - 2] << 8) + uint8[len - 1]
    parts.push(
      lookup[tmp >> 10] +
      lookup[(tmp >> 4) & 0x3F] +
      lookup[(tmp << 2) & 0x3F] +
      '='
    )
  }

  return parts.join('')
}

},{}],2:[function(require,module,exports){
(function (Buffer){(function (){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
/* eslint-disable no-proto */

'use strict'

var base64 = require('base64-js')
var ieee754 = require('ieee754')

exports.Buffer = Buffer
exports.SlowBuffer = SlowBuffer
exports.INSPECT_MAX_BYTES = 50

var K_MAX_LENGTH = 0x7fffffff
exports.kMaxLength = K_MAX_LENGTH

/**
 * If `Buffer.TYPED_ARRAY_SUPPORT`:
 *   === true    Use Uint8Array implementation (fastest)
 *   === false   Print warning and recommend using `buffer` v4.x which has an Object
 *               implementation (most compatible, even IE6)
 *
 * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,
 * Opera 11.6+, iOS 4.2+.
 *
 * We report that the browser does not support typed arrays if the are not subclassable
 * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`
 * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support
 * for __proto__ and has a buggy typed array implementation.
 */
Buffer.TYPED_ARRAY_SUPPORT = typedArraySupport()

if (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&
    typeof console.error === 'function') {
  console.error(
    'This browser lacks typed array (Uint8Array) support which is required by ' +
    '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'
  )
}

function typedArraySupport () {
  // Can typed array instances can be augmented?
  try {
    var arr = new Uint8Array(1)
    arr.__proto__ = { __proto__: Uint8Array.prototype, foo: function () { return 42 } }
    return arr.foo() === 42
  } catch (e) {
    return false
  }
}

Object.defineProperty(Buffer.prototype, 'parent', {
  enumerable: true,
  get: function () {
    if (!Buffer.isBuffer(this)) return undefined
    return this.buffer
  }
})

Object.defineProperty(Buffer.prototype, 'offset', {
  enumerable: true,
  get: function () {
    if (!Buffer.isBuffer(this)) return undefined
    return this.byteOffset
  }
})

function createBuffer (length) {
  if (length > K_MAX_LENGTH) {
    throw new RangeError('The value "' + length + '" is invalid for option "size"')
  }
  // Return an augmented `Uint8Array` instance
  var buf = new Uint8Array(length)
  buf.__proto__ = Buffer.prototype
  return buf
}

/**
 * The Buffer constructor returns instances of `Uint8Array` that have their
 * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of
 * `Uint8Array`, so the returned instances will have all the node `Buffer` methods
 * and the `Uint8Array` methods. Square bracket notation works as expected -- it
 * returns a single octet.
 *
 * The `Uint8Array` prototype remains unmodified.
 */

function Buffer (arg, encodingOrOffset, length) {
  // Common case.
  if (typeof arg === 'number') {
    if (typeof encodingOrOffset === 'string') {
      throw new TypeError(
        'The "string" argument must be of type string. Received type number'
      )
    }
    return allocUnsafe(arg)
  }
  return from(arg, encodingOrOffset, length)
}

// Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97
if (typeof Symbol !== 'undefined' && Symbol.species != null &&
    Buffer[Symbol.species] === Buffer) {
  Object.defineProperty(Buffer, Symbol.species, {
    value: null,
    configurable: true,
    enumerable: false,
    writable: false
  })
}

Buffer.poolSize = 8192 // not used by this implementation

function from (value, encodingOrOffset, length) {
  if (typeof value === 'string') {
    return fromString(value, encodingOrOffset)
  }

  if (ArrayBuffer.isView(value)) {
    return fromArrayLike(value)
  }

  if (value == null) {
    throw TypeError(
      'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +
      'or Array-like Object. Received type ' + (typeof value)
    )
  }

  if (isInstance(value, ArrayBuffer) ||
      (value && isInstance(value.buffer, ArrayBuffer))) {
    return fromArrayBuffer(value, encodingOrOffset, length)
  }

  if (typeof value === 'number') {
    throw new TypeError(
      'The "value" argument must not be of type number. Received type number'
    )
  }

  var valueOf = value.valueOf && value.valueOf()
  if (valueOf != null && valueOf !== value) {
    return Buffer.from(valueOf, encodingOrOffset, length)
  }

  var b = fromObject(value)
  if (b) return b

  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&
      typeof value[Symbol.toPrimitive] === 'function') {
    return Buffer.from(
      value[Symbol.toPrimitive]('string'), encodingOrOffset, length
    )
  }

  throw new TypeError(
    'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +
    'or Array-like Object. Received type ' + (typeof value)
  )
}

/**
 * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError
 * if value is a number.
 * Buffer.from(str[, encoding])
 * Buffer.from(array)
 * Buffer.from(buffer)
 * Buffer.from(arrayBuffer[, byteOffset[, length]])
 **/
Buffer.from = function (value, encodingOrOffset, length) {
  return from(value, encodingOrOffset, length)
}

// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:
// https://github.com/feross/buffer/pull/148
Buffer.prototype.__proto__ = Uint8Array.prototype
Buffer.__proto__ = Uint8Array

function assertSize (size) {
  if (typeof size !== 'number') {
    throw new TypeError('"size" argument must be of type number')
  } else if (size < 0) {
    throw new RangeError('The value "' + size + '" is invalid for option "size"')
  }
}

function alloc (size, fill, encoding) {
  assertSize(size)
  if (size <= 0) {
    return createBuffer(size)
  }
  if (fill !== undefined) {
    // Only pay attention to encoding if it's a string. This
    // prevents accidentally sending in a number that would
    // be interpretted as a start offset.
    return typeof encoding === 'string'
      ? createBuffer(size).fill(fill, encoding)
      : createBuffer(size).fill(fill)
  }
  return createBuffer(size)
}

/**
 * Creates a new filled Buffer instance.
 * alloc(size[, fill[, encoding]])
 **/
Buffer.alloc = function (size, fill, encoding) {
  return alloc(size, fill, encoding)
}

function allocUnsafe (size) {
  assertSize(size)
  return createBuffer(size < 0 ? 0 : checked(size) | 0)
}

/**
 * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.
 * */
Buffer.allocUnsafe = function (size) {
  return allocUnsafe(size)
}
/**
 * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.
 */
Buffer.allocUnsafeSlow = function (size) {
  return allocUnsafe(size)
}

function fromString (string, encoding) {
  if (typeof encoding !== 'string' || encoding === '') {
    encoding = 'utf8'
  }

  if (!Buffer.isEncoding(encoding)) {
    throw new TypeError('Unknown encoding: ' + encoding)
  }

  var length = byteLength(string, encoding) | 0
  var buf = createBuffer(length)

  var actual = buf.write(string, encoding)

  if (actual !== length) {
    // Writing a hex string, for example, that contains invalid characters will
    // cause everything after the first invalid character to be ignored. (e.g.
    // 'abxxcd' will be treated as 'ab')
    buf = buf.slice(0, actual)
  }

  return buf
}

function fromArrayLike (array) {
  var length = array.length < 0 ? 0 : checked(array.length) | 0
  var buf = createBuffer(length)
  for (var i = 0; i < length; i += 1) {
    buf[i] = array[i] & 255
  }
  return buf
}

function fromArrayBuffer (array, byteOffset, length) {
  if (byteOffset < 0 || array.byteLength < byteOffset) {
    throw new RangeError('"offset" is outside of buffer bounds')
  }

  if (array.byteLength < byteOffset + (length || 0)) {
    throw new RangeError('"length" is outside of buffer bounds')
  }

  var buf
  if (byteOffset === undefined && length === undefined) {
    buf = new Uint8Array(array)
  } else if (length === undefined) {
    buf = new Uint8Array(array, byteOffset)
  } else {
    buf = new Uint8Array(array, byteOffset, length)
  }

  // Return an augmented `Uint8Array` instance
  buf.__proto__ = Buffer.prototype
  return buf
}

function fromObject (obj) {
  if (Buffer.isBuffer(obj)) {
    var len = checked(obj.length) | 0
    var buf = createBuffer(len)

    if (buf.length === 0) {
      return buf
    }

    obj.copy(buf, 0, 0, len)
    return buf
  }

  if (obj.length !== undefined) {
    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {
      return createBuffer(0)
    }
    return fromArrayLike(obj)
  }

  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {
    return fromArrayLike(obj.data)
  }
}

function checked (length) {
  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when
  // length is NaN (which is otherwise coerced to zero.)
  if (length >= K_MAX_LENGTH) {
    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +
                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')
  }
  return length | 0
}

function SlowBuffer (length) {
  if (+length != length) { // eslint-disable-line eqeqeq
    length = 0
  }
  return Buffer.alloc(+length)
}

Buffer.isBuffer = function isBuffer (b) {
  return b != null && b._isBuffer === true &&
    b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false
}

Buffer.compare = function compare (a, b) {
  if (isInstance(a, Uint8Array)) a = Buffer.from(a, a.offset, a.byteLength)
  if (isInstance(b, Uint8Array)) b = Buffer.from(b, b.offset, b.byteLength)
  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {
    throw new TypeError(
      'The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array'
    )
  }

  if (a === b) return 0

  var x = a.length
  var y = b.length

  for (var i = 0, len = Math.min(x, y); i < len; ++i) {
    if (a[i] !== b[i]) {
      x = a[i]
      y = b[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

Buffer.isEncoding = function isEncoding (encoding) {
  switch (String(encoding).toLowerCase()) {
    case 'hex':
    case 'utf8':
    case 'utf-8':
    case 'ascii':
    case 'latin1':
    case 'binary':
    case 'base64':
    case 'ucs2':
    case 'ucs-2':
    case 'utf16le':
    case 'utf-16le':
      return true
    default:
      return false
  }
}

Buffer.concat = function concat (list, length) {
  if (!Array.isArray(list)) {
    throw new TypeError('"list" argument must be an Array of Buffers')
  }

  if (list.length === 0) {
    return Buffer.alloc(0)
  }

  var i
  if (length === undefined) {
    length = 0
    for (i = 0; i < list.length; ++i) {
      length += list[i].length
    }
  }

  var buffer = Buffer.allocUnsafe(length)
  var pos = 0
  for (i = 0; i < list.length; ++i) {
    var buf = list[i]
    if (isInstance(buf, Uint8Array)) {
      buf = Buffer.from(buf)
    }
    if (!Buffer.isBuffer(buf)) {
      throw new TypeError('"list" argument must be an Array of Buffers')
    }
    buf.copy(buffer, pos)
    pos += buf.length
  }
  return buffer
}

function byteLength (string, encoding) {
  if (Buffer.isBuffer(string)) {
    return string.length
  }
  if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {
    return string.byteLength
  }
  if (typeof string !== 'string') {
    throw new TypeError(
      'The "string" argument must be one of type string, Buffer, or ArrayBuffer. ' +
      'Received type ' + typeof string
    )
  }

  var len = string.length
  var mustMatch = (arguments.length > 2 && arguments[2] === true)
  if (!mustMatch && len === 0) return 0

  // Use a for loop to avoid recursion
  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'ascii':
      case 'latin1':
      case 'binary':
        return len
      case 'utf8':
      case 'utf-8':
        return utf8ToBytes(string).length
      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return len * 2
      case 'hex':
        return len >>> 1
      case 'base64':
        return base64ToBytes(string).length
      default:
        if (loweredCase) {
          return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8
        }
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}
Buffer.byteLength = byteLength

function slowToString (encoding, start, end) {
  var loweredCase = false

  // No need to verify that "this.length <= MAX_UINT32" since it's a read-only
  // property of a typed array.

  // This behaves neither like String nor Uint8Array in that we set start/end
  // to their upper/lower bounds if the value passed is out of range.
  // undefined is handled specially as per ECMA-262 6th Edition,
  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.
  if (start === undefined || start < 0) {
    start = 0
  }
  // Return early if start > this.length. Done here to prevent potential uint32
  // coercion fail below.
  if (start > this.length) {
    return ''
  }

  if (end === undefined || end > this.length) {
    end = this.length
  }

  if (end <= 0) {
    return ''
  }

  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.
  end >>>= 0
  start >>>= 0

  if (end <= start) {
    return ''
  }

  if (!encoding) encoding = 'utf8'

  while (true) {
    switch (encoding) {
      case 'hex':
        return hexSlice(this, start, end)

      case 'utf8':
      case 'utf-8':
        return utf8Slice(this, start, end)

      case 'ascii':
        return asciiSlice(this, start, end)

      case 'latin1':
      case 'binary':
        return latin1Slice(this, start, end)

      case 'base64':
        return base64Slice(this, start, end)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return utf16leSlice(this, start, end)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = (encoding + '').toLowerCase()
        loweredCase = true
    }
  }
}

// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)
// to detect a Buffer instance. It's not possible to use `instanceof Buffer`
// reliably in a browserify context because there could be multiple different
// copies of the 'buffer' package in use. This method works even for Buffer
// instances that were created from another copy of the `buffer` package.
// See: https://github.com/feross/buffer/issues/154
Buffer.prototype._isBuffer = true

function swap (b, n, m) {
  var i = b[n]
  b[n] = b[m]
  b[m] = i
}

Buffer.prototype.swap16 = function swap16 () {
  var len = this.length
  if (len % 2 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 16-bits')
  }
  for (var i = 0; i < len; i += 2) {
    swap(this, i, i + 1)
  }
  return this
}

Buffer.prototype.swap32 = function swap32 () {
  var len = this.length
  if (len % 4 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 32-bits')
  }
  for (var i = 0; i < len; i += 4) {
    swap(this, i, i + 3)
    swap(this, i + 1, i + 2)
  }
  return this
}

Buffer.prototype.swap64 = function swap64 () {
  var len = this.length
  if (len % 8 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 64-bits')
  }
  for (var i = 0; i < len; i += 8) {
    swap(this, i, i + 7)
    swap(this, i + 1, i + 6)
    swap(this, i + 2, i + 5)
    swap(this, i + 3, i + 4)
  }
  return this
}

Buffer.prototype.toString = function toString () {
  var length = this.length
  if (length === 0) return ''
  if (arguments.length === 0) return utf8Slice(this, 0, length)
  return slowToString.apply(this, arguments)
}

Buffer.prototype.toLocaleString = Buffer.prototype.toString

Buffer.prototype.equals = function equals (b) {
  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')
  if (this === b) return true
  return Buffer.compare(this, b) === 0
}

Buffer.prototype.inspect = function inspect () {
  var str = ''
  var max = exports.INSPECT_MAX_BYTES
  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()
  if (this.length > max) str += ' ... '
  return '<Buffer ' + str + '>'
}

Buffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {
  if (isInstance(target, Uint8Array)) {
    target = Buffer.from(target, target.offset, target.byteLength)
  }
  if (!Buffer.isBuffer(target)) {
    throw new TypeError(
      'The "target" argument must be one of type Buffer or Uint8Array. ' +
      'Received type ' + (typeof target)
    )
  }

  if (start === undefined) {
    start = 0
  }
  if (end === undefined) {
    end = target ? target.length : 0
  }
  if (thisStart === undefined) {
    thisStart = 0
  }
  if (thisEnd === undefined) {
    thisEnd = this.length
  }

  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {
    throw new RangeError('out of range index')
  }

  if (thisStart >= thisEnd && start >= end) {
    return 0
  }
  if (thisStart >= thisEnd) {
    return -1
  }
  if (start >= end) {
    return 1
  }

  start >>>= 0
  end >>>= 0
  thisStart >>>= 0
  thisEnd >>>= 0

  if (this === target) return 0

  var x = thisEnd - thisStart
  var y = end - start
  var len = Math.min(x, y)

  var thisCopy = this.slice(thisStart, thisEnd)
  var targetCopy = target.slice(start, end)

  for (var i = 0; i < len; ++i) {
    if (thisCopy[i] !== targetCopy[i]) {
      x = thisCopy[i]
      y = targetCopy[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,
// OR the last index of `val` in `buffer` at offset <= `byteOffset`.
//
// Arguments:
// - buffer - a Buffer to search
// - val - a string, Buffer, or number
// - byteOffset - an index into `buffer`; will be clamped to an int32
// - encoding - an optional encoding, relevant is val is a string
// - dir - true for indexOf, false for lastIndexOf
function bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {
  // Empty buffer means no match
  if (buffer.length === 0) return -1

  // Normalize byteOffset
  if (typeof byteOffset === 'string') {
    encoding = byteOffset
    byteOffset = 0
  } else if (byteOffset > 0x7fffffff) {
    byteOffset = 0x7fffffff
  } else if (byteOffset < -0x80000000) {
    byteOffset = -0x80000000
  }
  byteOffset = +byteOffset // Coerce to Number.
  if (numberIsNaN(byteOffset)) {
    // byteOffset: it it's undefined, null, NaN, "foo", etc, search whole buffer
    byteOffset = dir ? 0 : (buffer.length - 1)
  }

  // Normalize byteOffset: negative offsets start from the end of the buffer
  if (byteOffset < 0) byteOffset = buffer.length + byteOffset
  if (byteOffset >= buffer.length) {
    if (dir) return -1
    else byteOffset = buffer.length - 1
  } else if (byteOffset < 0) {
    if (dir) byteOffset = 0
    else return -1
  }

  // Normalize val
  if (typeof val === 'string') {
    val = Buffer.from(val, encoding)
  }

  // Finally, search either indexOf (if dir is true) or lastIndexOf
  if (Buffer.isBuffer(val)) {
    // Special case: looking for empty string/buffer always fails
    if (val.length === 0) {
      return -1
    }
    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)
  } else if (typeof val === 'number') {
    val = val & 0xFF // Search for a byte value [0-255]
    if (typeof Uint8Array.prototype.indexOf === 'function') {
      if (dir) {
        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)
      } else {
        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)
      }
    }
    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)
  }

  throw new TypeError('val must be string, number or Buffer')
}

function arrayIndexOf (arr, val, byteOffset, encoding, dir) {
  var indexSize = 1
  var arrLength = arr.length
  var valLength = val.length

  if (encoding !== undefined) {
    encoding = String(encoding).toLowerCase()
    if (encoding === 'ucs2' || encoding === 'ucs-2' ||
        encoding === 'utf16le' || encoding === 'utf-16le') {
      if (arr.length < 2 || val.length < 2) {
        return -1
      }
      indexSize = 2
      arrLength /= 2
      valLength /= 2
      byteOffset /= 2
    }
  }

  function read (buf, i) {
    if (indexSize === 1) {
      return buf[i]
    } else {
      return buf.readUInt16BE(i * indexSize)
    }
  }

  var i
  if (dir) {
    var foundIndex = -1
    for (i = byteOffset; i < arrLength; i++) {
      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {
        if (foundIndex === -1) foundIndex = i
        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize
      } else {
        if (foundIndex !== -1) i -= i - foundIndex
        foundIndex = -1
      }
    }
  } else {
    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength
    for (i = byteOffset; i >= 0; i--) {
      var found = true
      for (var j = 0; j < valLength; j++) {
        if (read(arr, i + j) !== read(val, j)) {
          found = false
          break
        }
      }
      if (found) return i
    }
  }

  return -1
}

Buffer.prototype.includes = function includes (val, byteOffset, encoding) {
  return this.indexOf(val, byteOffset, encoding) !== -1
}

Buffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)
}

Buffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)
}

function hexWrite (buf, string, offset, length) {
  offset = Number(offset) || 0
  var remaining = buf.length - offset
  if (!length) {
    length = remaining
  } else {
    length = Number(length)
    if (length > remaining) {
      length = remaining
    }
  }

  var strLen = string.length

  if (length > strLen / 2) {
    length = strLen / 2
  }
  for (var i = 0; i < length; ++i) {
    var parsed = parseInt(string.substr(i * 2, 2), 16)
    if (numberIsNaN(parsed)) return i
    buf[offset + i] = parsed
  }
  return i
}

function utf8Write (buf, string, offset, length) {
  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)
}

function asciiWrite (buf, string, offset, length) {
  return blitBuffer(asciiToBytes(string), buf, offset, length)
}

function latin1Write (buf, string, offset, length) {
  return asciiWrite(buf, string, offset, length)
}

function base64Write (buf, string, offset, length) {
  return blitBuffer(base64ToBytes(string), buf, offset, length)
}

function ucs2Write (buf, string, offset, length) {
  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)
}

Buffer.prototype.write = function write (string, offset, length, encoding) {
  // Buffer#write(string)
  if (offset === undefined) {
    encoding = 'utf8'
    length = this.length
    offset = 0
  // Buffer#write(string, encoding)
  } else if (length === undefined && typeof offset === 'string') {
    encoding = offset
    length = this.length
    offset = 0
  // Buffer#write(string, offset[, length][, encoding])
  } else if (isFinite(offset)) {
    offset = offset >>> 0
    if (isFinite(length)) {
      length = length >>> 0
      if (encoding === undefined) encoding = 'utf8'
    } else {
      encoding = length
      length = undefined
    }
  } else {
    throw new Error(
      'Buffer.write(string, encoding, offset[, length]) is no longer supported'
    )
  }

  var remaining = this.length - offset
  if (length === undefined || length > remaining) length = remaining

  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {
    throw new RangeError('Attempt to write outside buffer bounds')
  }

  if (!encoding) encoding = 'utf8'

  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'hex':
        return hexWrite(this, string, offset, length)

      case 'utf8':
      case 'utf-8':
        return utf8Write(this, string, offset, length)

      case 'ascii':
        return asciiWrite(this, string, offset, length)

      case 'latin1':
      case 'binary':
        return latin1Write(this, string, offset, length)

      case 'base64':
        // Warning: maxLength not taken into account in base64Write
        return base64Write(this, string, offset, length)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return ucs2Write(this, string, offset, length)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}

Buffer.prototype.toJSON = function toJSON () {
  return {
    type: 'Buffer',
    data: Array.prototype.slice.call(this._arr || this, 0)
  }
}

function base64Slice (buf, start, end) {
  if (start === 0 && end === buf.length) {
    return base64.fromByteArray(buf)
  } else {
    return base64.fromByteArray(buf.slice(start, end))
  }
}

function utf8Slice (buf, start, end) {
  end = Math.min(buf.length, end)
  var res = []

  var i = start
  while (i < end) {
    var firstByte = buf[i]
    var codePoint = null
    var bytesPerSequence = (firstByte > 0xEF) ? 4
      : (firstByte > 0xDF) ? 3
        : (firstByte > 0xBF) ? 2
          : 1

    if (i + bytesPerSequence <= end) {
      var secondByte, thirdByte, fourthByte, tempCodePoint

      switch (bytesPerSequence) {
        case 1:
          if (firstByte < 0x80) {
            codePoint = firstByte
          }
          break
        case 2:
          secondByte = buf[i + 1]
          if ((secondByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)
            if (tempCodePoint > 0x7F) {
              codePoint = tempCodePoint
            }
          }
          break
        case 3:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)
            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {
              codePoint = tempCodePoint
            }
          }
          break
        case 4:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          fourthByte = buf[i + 3]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)
            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {
              codePoint = tempCodePoint
            }
          }
      }
    }

    if (codePoint === null) {
      // we did not generate a valid codePoint so insert a
      // replacement char (U+FFFD) and advance only 1 byte
      codePoint = 0xFFFD
      bytesPerSequence = 1
    } else if (codePoint > 0xFFFF) {
      // encode to utf16 (surrogate pair dance)
      codePoint -= 0x10000
      res.push(codePoint >>> 10 & 0x3FF | 0xD800)
      codePoint = 0xDC00 | codePoint & 0x3FF
    }

    res.push(codePoint)
    i += bytesPerSequence
  }

  return decodeCodePointsArray(res)
}

// Based on http://stackoverflow.com/a/22747272/680742, the browser with
// the lowest limit is Chrome, with 0x10000 args.
// We go 1 magnitude less, for safety
var MAX_ARGUMENTS_LENGTH = 0x1000

function decodeCodePointsArray (codePoints) {
  var len = codePoints.length
  if (len <= MAX_ARGUMENTS_LENGTH) {
    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()
  }

  // Decode in chunks to avoid "call stack size exceeded".
  var res = ''
  var i = 0
  while (i < len) {
    res += String.fromCharCode.apply(
      String,
      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)
    )
  }
  return res
}

function asciiSlice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i] & 0x7F)
  }
  return ret
}

function latin1Slice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i])
  }
  return ret
}

function hexSlice (buf, start, end) {
  var len = buf.length

  if (!start || start < 0) start = 0
  if (!end || end < 0 || end > len) end = len

  var out = ''
  for (var i = start; i < end; ++i) {
    out += toHex(buf[i])
  }
  return out
}

function utf16leSlice (buf, start, end) {
  var bytes = buf.slice(start, end)
  var res = ''
  for (var i = 0; i < bytes.length; i += 2) {
    res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))
  }
  return res
}

Buffer.prototype.slice = function slice (start, end) {
  var len = this.length
  start = ~~start
  end = end === undefined ? len : ~~end

  if (start < 0) {
    start += len
    if (start < 0) start = 0
  } else if (start > len) {
    start = len
  }

  if (end < 0) {
    end += len
    if (end < 0) end = 0
  } else if (end > len) {
    end = len
  }

  if (end < start) end = start

  var newBuf = this.subarray(start, end)
  // Return an augmented `Uint8Array` instance
  newBuf.__proto__ = Buffer.prototype
  return newBuf
}

/*
 * Need to make sure that buffer isn't trying to write out of bounds.
 */
function checkOffset (offset, ext, length) {
  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')
  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')
}

Buffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }

  return val
}

Buffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) {
    checkOffset(offset, byteLength, this.length)
  }

  var val = this[offset + --byteLength]
  var mul = 1
  while (byteLength > 0 && (mul *= 0x100)) {
    val += this[offset + --byteLength] * mul
  }

  return val
}

Buffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 1, this.length)
  return this[offset]
}

Buffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  return this[offset] | (this[offset + 1] << 8)
}

Buffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  return (this[offset] << 8) | this[offset + 1]
}

Buffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return ((this[offset]) |
      (this[offset + 1] << 8) |
      (this[offset + 2] << 16)) +
      (this[offset + 3] * 0x1000000)
}

Buffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] * 0x1000000) +
    ((this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    this[offset + 3])
}

Buffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var i = byteLength
  var mul = 1
  var val = this[offset + --i]
  while (i > 0 && (mul *= 0x100)) {
    val += this[offset + --i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readInt8 = function readInt8 (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 1, this.length)
  if (!(this[offset] & 0x80)) return (this[offset])
  return ((0xff - this[offset] + 1) * -1)
}

Buffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset] | (this[offset + 1] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset + 1] | (this[offset] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset]) |
    (this[offset + 1] << 8) |
    (this[offset + 2] << 16) |
    (this[offset + 3] << 24)
}

Buffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] << 24) |
    (this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    (this[offset + 3])
}

Buffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, true, 23, 4)
}

Buffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, false, 23, 4)
}

Buffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, true, 52, 8)
}

Buffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, false, 52, 8)
}

function checkInt (buf, value, offset, ext, max, min) {
  if (!Buffer.isBuffer(buf)) throw new TypeError('"buffer" argument must be a Buffer instance')
  if (value > max || value < min) throw new RangeError('"value" argument is out of bounds')
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
}

Buffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var mul = 1
  var i = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var i = byteLength - 1
  var mul = 1
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)
  this[offset] = (value & 0xff)
  return offset + 1
}

Buffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  this[offset] = (value & 0xff)
  this[offset + 1] = (value >>> 8)
  return offset + 2
}

Buffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  this[offset] = (value >>> 8)
  this[offset + 1] = (value & 0xff)
  return offset + 2
}

Buffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  this[offset + 3] = (value >>> 24)
  this[offset + 2] = (value >>> 16)
  this[offset + 1] = (value >>> 8)
  this[offset] = (value & 0xff)
  return offset + 4
}

Buffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  this[offset] = (value >>> 24)
  this[offset + 1] = (value >>> 16)
  this[offset + 2] = (value >>> 8)
  this[offset + 3] = (value & 0xff)
  return offset + 4
}

Buffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    var limit = Math.pow(2, (8 * byteLength) - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = 0
  var mul = 1
  var sub = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    var limit = Math.pow(2, (8 * byteLength) - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = byteLength - 1
  var mul = 1
  var sub = 0
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)
  if (value < 0) value = 0xff + value + 1
  this[offset] = (value & 0xff)
  return offset + 1
}

Buffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  this[offset] = (value & 0xff)
  this[offset + 1] = (value >>> 8)
  return offset + 2
}

Buffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  this[offset] = (value >>> 8)
  this[offset + 1] = (value & 0xff)
  return offset + 2
}

Buffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  this[offset] = (value & 0xff)
  this[offset + 1] = (value >>> 8)
  this[offset + 2] = (value >>> 16)
  this[offset + 3] = (value >>> 24)
  return offset + 4
}

Buffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  if (value < 0) value = 0xffffffff + value + 1
  this[offset] = (value >>> 24)
  this[offset + 1] = (value >>> 16)
  this[offset + 2] = (value >>> 8)
  this[offset + 3] = (value & 0xff)
  return offset + 4
}

function checkIEEE754 (buf, value, offset, ext, max, min) {
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
  if (offset < 0) throw new RangeError('Index out of range')
}

function writeFloat (buf, value, offset, littleEndian, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)
  }
  ieee754.write(buf, value, offset, littleEndian, 23, 4)
  return offset + 4
}

Buffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {
  return writeFloat(this, value, offset, true, noAssert)
}

Buffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {
  return writeFloat(this, value, offset, false, noAssert)
}

function writeDouble (buf, value, offset, littleEndian, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)
  }
  ieee754.write(buf, value, offset, littleEndian, 52, 8)
  return offset + 8
}

Buffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {
  return writeDouble(this, value, offset, true, noAssert)
}

Buffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {
  return writeDouble(this, value, offset, false, noAssert)
}

// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)
Buffer.prototype.copy = function copy (target, targetStart, start, end) {
  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')
  if (!start) start = 0
  if (!end && end !== 0) end = this.length
  if (targetStart >= target.length) targetStart = target.length
  if (!targetStart) targetStart = 0
  if (end > 0 && end < start) end = start

  // Copy 0 bytes; we're done
  if (end === start) return 0
  if (target.length === 0 || this.length === 0) return 0

  // Fatal error conditions
  if (targetStart < 0) {
    throw new RangeError('targetStart out of bounds')
  }
  if (start < 0 || start >= this.length) throw new RangeError('Index out of range')
  if (end < 0) throw new RangeError('sourceEnd out of bounds')

  // Are we oob?
  if (end > this.length) end = this.length
  if (target.length - targetStart < end - start) {
    end = target.length - targetStart + start
  }

  var len = end - start

  if (this === target && typeof Uint8Array.prototype.copyWithin === 'function') {
    // Use built-in when available, missing from IE11
    this.copyWithin(targetStart, start, end)
  } else if (this === target && start < targetStart && targetStart < end) {
    // descending copy from end
    for (var i = len - 1; i >= 0; --i) {
      target[i + targetStart] = this[i + start]
    }
  } else {
    Uint8Array.prototype.set.call(
      target,
      this.subarray(start, end),
      targetStart
    )
  }

  return len
}

// Usage:
//    buffer.fill(number[, offset[, end]])
//    buffer.fill(buffer[, offset[, end]])
//    buffer.fill(string[, offset[, end]][, encoding])
Buffer.prototype.fill = function fill (val, start, end, encoding) {
  // Handle string cases:
  if (typeof val === 'string') {
    if (typeof start === 'string') {
      encoding = start
      start = 0
      end = this.length
    } else if (typeof end === 'string') {
      encoding = end
      end = this.length
    }
    if (encoding !== undefined && typeof encoding !== 'string') {
      throw new TypeError('encoding must be a string')
    }
    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {
      throw new TypeError('Unknown encoding: ' + encoding)
    }
    if (val.length === 1) {
      var code = val.charCodeAt(0)
      if ((encoding === 'utf8' && code < 128) ||
          encoding === 'latin1') {
        // Fast path: If `val` fits into a single byte, use that numeric value.
        val = code
      }
    }
  } else if (typeof val === 'number') {
    val = val & 255
  }

  // Invalid ranges are not set to a default, so can range check early.
  if (start < 0 || this.length < start || this.length < end) {
    throw new RangeError('Out of range index')
  }

  if (end <= start) {
    return this
  }

  start = start >>> 0
  end = end === undefined ? this.length : end >>> 0

  if (!val) val = 0

  var i
  if (typeof val === 'number') {
    for (i = start; i < end; ++i) {
      this[i] = val
    }
  } else {
    var bytes = Buffer.isBuffer(val)
      ? val
      : Buffer.from(val, encoding)
    var len = bytes.length
    if (len === 0) {
      throw new TypeError('The value "' + val +
        '" is invalid for argument "value"')
    }
    for (i = 0; i < end - start; ++i) {
      this[i + start] = bytes[i % len]
    }
  }

  return this
}

// HELPER FUNCTIONS
// ================

var INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g

function base64clean (str) {
  // Node takes equal signs as end of the Base64 encoding
  str = str.split('=')[0]
  // Node strips out invalid characters like \n and \t from the string, base64-js does not
  str = str.trim().replace(INVALID_BASE64_RE, '')
  // Node converts strings with length < 2 to ''
  if (str.length < 2) return ''
  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not
  while (str.length % 4 !== 0) {
    str = str + '='
  }
  return str
}

function toHex (n) {
  if (n < 16) return '0' + n.toString(16)
  return n.toString(16)
}

function utf8ToBytes (string, units) {
  units = units || Infinity
  var codePoint
  var length = string.length
  var leadSurrogate = null
  var bytes = []

  for (var i = 0; i < length; ++i) {
    codePoint = string.charCodeAt(i)

    // is surrogate component
    if (codePoint > 0xD7FF && codePoint < 0xE000) {
      // last char was a lead
      if (!leadSurrogate) {
        // no lead yet
        if (codePoint > 0xDBFF) {
          // unexpected trail
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        } else if (i + 1 === length) {
          // unpaired lead
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        }

        // valid lead
        leadSurrogate = codePoint

        continue
      }

      // 2 leads in a row
      if (codePoint < 0xDC00) {
        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
        leadSurrogate = codePoint
        continue
      }

      // valid surrogate pair
      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000
    } else if (leadSurrogate) {
      // valid bmp char, but last char was a lead
      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
    }

    leadSurrogate = null

    // encode utf8
    if (codePoint < 0x80) {
      if ((units -= 1) < 0) break
      bytes.push(codePoint)
    } else if (codePoint < 0x800) {
      if ((units -= 2) < 0) break
      bytes.push(
        codePoint >> 0x6 | 0xC0,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x10000) {
      if ((units -= 3) < 0) break
      bytes.push(
        codePoint >> 0xC | 0xE0,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x110000) {
      if ((units -= 4) < 0) break
      bytes.push(
        codePoint >> 0x12 | 0xF0,
        codePoint >> 0xC & 0x3F | 0x80,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else {
      throw new Error('Invalid code point')
    }
  }

  return bytes
}

function asciiToBytes (str) {
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    // Node's code seems to be doing this and not & 0x7F..
    byteArray.push(str.charCodeAt(i) & 0xFF)
  }
  return byteArray
}

function utf16leToBytes (str, units) {
  var c, hi, lo
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    if ((units -= 2) < 0) break

    c = str.charCodeAt(i)
    hi = c >> 8
    lo = c % 256
    byteArray.push(lo)
    byteArray.push(hi)
  }

  return byteArray
}

function base64ToBytes (str) {
  return base64.toByteArray(base64clean(str))
}

function blitBuffer (src, dst, offset, length) {
  for (var i = 0; i < length; ++i) {
    if ((i + offset >= dst.length) || (i >= src.length)) break
    dst[i + offset] = src[i]
  }
  return i
}

// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass
// the `instanceof` check but they should be treated as of that type.
// See: https://github.com/feross/buffer/issues/166
function isInstance (obj, type) {
  return obj instanceof type ||
    (obj != null && obj.constructor != null && obj.constructor.name != null &&
      obj.constructor.name === type.name)
}
function numberIsNaN (obj) {
  // For IE11 support
  return obj !== obj // eslint-disable-line no-self-compare
}

}).call(this)}).call(this,require("buffer").Buffer)

},{"base64-js":1,"buffer":2,"ieee754":3}],3:[function(require,module,exports){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
exports.read = function (buffer, offset, isLE, mLen, nBytes) {
  var e, m
  var eLen = (nBytes * 8) - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var nBits = -7
  var i = isLE ? (nBytes - 1) : 0
  var d = isLE ? -1 : 1
  var s = buffer[offset + i]

  i += d

  e = s & ((1 << (-nBits)) - 1)
  s >>= (-nBits)
  nBits += eLen
  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}

  m = e & ((1 << (-nBits)) - 1)
  e >>= (-nBits)
  nBits += mLen
  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}

  if (e === 0) {
    e = 1 - eBias
  } else if (e === eMax) {
    return m ? NaN : ((s ? -1 : 1) * Infinity)
  } else {
    m = m + Math.pow(2, mLen)
    e = e - eBias
  }
  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)
}

exports.write = function (buffer, value, offset, isLE, mLen, nBytes) {
  var e, m, c
  var eLen = (nBytes * 8) - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)
  var i = isLE ? 0 : (nBytes - 1)
  var d = isLE ? 1 : -1
  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0

  value = Math.abs(value)

  if (isNaN(value) || value === Infinity) {
    m = isNaN(value) ? 1 : 0
    e = eMax
  } else {
    e = Math.floor(Math.log(value) / Math.LN2)
    if (value * (c = Math.pow(2, -e)) < 1) {
      e--
      c *= 2
    }
    if (e + eBias >= 1) {
      value += rt / c
    } else {
      value += rt * Math.pow(2, 1 - eBias)
    }
    if (value * c >= 2) {
      e++
      c /= 2
    }

    if (e + eBias >= eMax) {
      m = 0
      e = eMax
    } else if (e + eBias >= 1) {
      m = ((value * c) - 1) * Math.pow(2, mLen)
      e = e + eBias
    } else {
      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)
      e = 0
    }
  }

  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}

  e = (e << mLen) | m
  eLen += mLen
  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}

  buffer[offset + i - d] |= s * 128
}

},{}],4:[function(require,module,exports){
(function (global,Buffer){(function (){
//
// THIS FILE IS AUTOMATICALLY GENERATED! DO NOT EDIT BY HAND!
//
;
(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined'
        ? module.exports = factory()
        : typeof define === 'function' && define.amd
            ? define(factory) :
            // cf. https://github.com/dankogai/js-base64/issues/119
            (function () {
                // existing version for noConflict()
                var _Base64 = global.Base64;
                var gBase64 = factory();
                gBase64.noConflict = function () {
                    global.Base64 = _Base64;
                    return gBase64;
                };
                if (global.Meteor) { // Meteor.js
                    Base64 = gBase64;
                }
                global.Base64 = gBase64;
            })();
}((typeof self !== 'undefined' ? self
    : typeof window !== 'undefined' ? window
        : typeof global !== 'undefined' ? global
            : this), function () {
    'use strict';
    /**
     *  base64.ts
     *
     *  Licensed under the BSD 3-Clause License.
     *    http://opensource.org/licenses/BSD-3-Clause
     *
     *  References:
     *    http://en.wikipedia.org/wiki/Base64
     *
     * <AUTHOR> Kogai (https://github.com/dankogai)
     */
    var version = '3.7.2';
    /**
     * @deprecated use lowercase `version`.
     */
    var VERSION = version;
    var _hasatob = typeof atob === 'function';
    var _hasbtoa = typeof btoa === 'function';
    var _hasBuffer = typeof Buffer === 'function';
    var _TD = typeof TextDecoder === 'function' ? new TextDecoder() : undefined;
    var _TE = typeof TextEncoder === 'function' ? new TextEncoder() : undefined;
    var b64ch = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
    var b64chs = Array.prototype.slice.call(b64ch);
    var b64tab = (function (a) {
        var tab = {};
        a.forEach(function (c, i) { return tab[c] = i; });
        return tab;
    })(b64chs);
    var b64re = /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;
    var _fromCC = String.fromCharCode.bind(String);
    var _U8Afrom = typeof Uint8Array.from === 'function'
        ? Uint8Array.from.bind(Uint8Array)
        : function (it, fn) {
            if (fn === void 0) { fn = function (x) { return x; }; }
            return new Uint8Array(Array.prototype.slice.call(it, 0).map(fn));
        };
    var _mkUriSafe = function (src) { return src
        .replace(/=/g, '').replace(/[+\/]/g, function (m0) { return m0 == '+' ? '-' : '_'; }); };
    var _tidyB64 = function (s) { return s.replace(/[^A-Za-z0-9\+\/]/g, ''); };
    /**
     * polyfill version of `btoa`
     */
    var btoaPolyfill = function (bin) {
        // console.log('polyfilled');
        var u32, c0, c1, c2, asc = '';
        var pad = bin.length % 3;
        for (var i = 0; i < bin.length;) {
            if ((c0 = bin.charCodeAt(i++)) > 255 ||
                (c1 = bin.charCodeAt(i++)) > 255 ||
                (c2 = bin.charCodeAt(i++)) > 255)
                throw new TypeError('invalid character found');
            u32 = (c0 << 16) | (c1 << 8) | c2;
            asc += b64chs[u32 >> 18 & 63]
                + b64chs[u32 >> 12 & 63]
                + b64chs[u32 >> 6 & 63]
                + b64chs[u32 & 63];
        }
        return pad ? asc.slice(0, pad - 3) + "===".substring(pad) : asc;
    };
    /**
     * does what `window.btoa` of web browsers do.
     * @param {String} bin binary string
     * @returns {string} Base64-encoded string
     */
    var _btoa = _hasbtoa ? function (bin) { return btoa(bin); }
        : _hasBuffer ? function (bin) { return Buffer.from(bin, 'binary').toString('base64'); }
            : btoaPolyfill;
    var _fromUint8Array = _hasBuffer
        ? function (u8a) { return Buffer.from(u8a).toString('base64'); }
        : function (u8a) {
            // cf. https://stackoverflow.com/questions/12710001/how-to-convert-uint8-array-to-base64-encoded-string/12713326#12713326
            var maxargs = 0x1000;
            var strs = [];
            for (var i = 0, l = u8a.length; i < l; i += maxargs) {
                strs.push(_fromCC.apply(null, u8a.subarray(i, i + maxargs)));
            }
            return _btoa(strs.join(''));
        };
    /**
     * converts a Uint8Array to a Base64 string.
     * @param {boolean} [urlsafe] URL-and-filename-safe a la RFC4648 §5
     * @returns {string} Base64 string
     */
    var fromUint8Array = function (u8a, urlsafe) {
        if (urlsafe === void 0) { urlsafe = false; }
        return urlsafe ? _mkUriSafe(_fromUint8Array(u8a)) : _fromUint8Array(u8a);
    };
    // This trick is found broken https://github.com/dankogai/js-base64/issues/130
    // const utob = (src: string) => unescape(encodeURIComponent(src));
    // reverting good old fationed regexp
    var cb_utob = function (c) {
        if (c.length < 2) {
            var cc = c.charCodeAt(0);
            return cc < 0x80 ? c
                : cc < 0x800 ? (_fromCC(0xc0 | (cc >>> 6))
                    + _fromCC(0x80 | (cc & 0x3f)))
                    : (_fromCC(0xe0 | ((cc >>> 12) & 0x0f))
                        + _fromCC(0x80 | ((cc >>> 6) & 0x3f))
                        + _fromCC(0x80 | (cc & 0x3f)));
        }
        else {
            var cc = 0x10000
                + (c.charCodeAt(0) - 0xD800) * 0x400
                + (c.charCodeAt(1) - 0xDC00);
            return (_fromCC(0xf0 | ((cc >>> 18) & 0x07))
                + _fromCC(0x80 | ((cc >>> 12) & 0x3f))
                + _fromCC(0x80 | ((cc >>> 6) & 0x3f))
                + _fromCC(0x80 | (cc & 0x3f)));
        }
    };
    var re_utob = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
    /**
     * @deprecated should have been internal use only.
     * @param {string} src UTF-8 string
     * @returns {string} UTF-16 string
     */
    var utob = function (u) { return u.replace(re_utob, cb_utob); };
    //
    var _encode = _hasBuffer
        ? function (s) { return Buffer.from(s, 'utf8').toString('base64'); }
        : _TE
            ? function (s) { return _fromUint8Array(_TE.encode(s)); }
            : function (s) { return _btoa(utob(s)); };
    /**
     * converts a UTF-8-encoded string to a Base64 string.
     * @param {boolean} [urlsafe] if `true` make the result URL-safe
     * @returns {string} Base64 string
     */
    var encode = function (src, urlsafe) {
        if (urlsafe === void 0) { urlsafe = false; }
        return urlsafe
            ? _mkUriSafe(_encode(src))
            : _encode(src);
    };
    /**
     * converts a UTF-8-encoded string to URL-safe Base64 RFC4648 §5.
     * @returns {string} Base64 string
     */
    var encodeURI = function (src) { return encode(src, true); };
    // This trick is found broken https://github.com/dankogai/js-base64/issues/130
    // const btou = (src: string) => decodeURIComponent(escape(src));
    // reverting good old fationed regexp
    var re_btou = /[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g;
    var cb_btou = function (cccc) {
        switch (cccc.length) {
            case 4:
                var cp = ((0x07 & cccc.charCodeAt(0)) << 18)
                    | ((0x3f & cccc.charCodeAt(1)) << 12)
                    | ((0x3f & cccc.charCodeAt(2)) << 6)
                    | (0x3f & cccc.charCodeAt(3)), offset = cp - 0x10000;
                return (_fromCC((offset >>> 10) + 0xD800)
                    + _fromCC((offset & 0x3FF) + 0xDC00));
            case 3:
                return _fromCC(((0x0f & cccc.charCodeAt(0)) << 12)
                    | ((0x3f & cccc.charCodeAt(1)) << 6)
                    | (0x3f & cccc.charCodeAt(2)));
            default:
                return _fromCC(((0x1f & cccc.charCodeAt(0)) << 6)
                    | (0x3f & cccc.charCodeAt(1)));
        }
    };
    /**
     * @deprecated should have been internal use only.
     * @param {string} src UTF-16 string
     * @returns {string} UTF-8 string
     */
    var btou = function (b) { return b.replace(re_btou, cb_btou); };
    /**
     * polyfill version of `atob`
     */
    var atobPolyfill = function (asc) {
        // console.log('polyfilled');
        asc = asc.replace(/\s+/g, '');
        if (!b64re.test(asc))
            throw new TypeError('malformed base64.');
        asc += '=='.slice(2 - (asc.length & 3));
        var u24, bin = '', r1, r2;
        for (var i = 0; i < asc.length;) {
            u24 = b64tab[asc.charAt(i++)] << 18
                | b64tab[asc.charAt(i++)] << 12
                | (r1 = b64tab[asc.charAt(i++)]) << 6
                | (r2 = b64tab[asc.charAt(i++)]);
            bin += r1 === 64 ? _fromCC(u24 >> 16 & 255)
                : r2 === 64 ? _fromCC(u24 >> 16 & 255, u24 >> 8 & 255)
                    : _fromCC(u24 >> 16 & 255, u24 >> 8 & 255, u24 & 255);
        }
        return bin;
    };
    /**
     * does what `window.atob` of web browsers do.
     * @param {String} asc Base64-encoded string
     * @returns {string} binary string
     */
    var _atob = _hasatob ? function (asc) { return atob(_tidyB64(asc)); }
        : _hasBuffer ? function (asc) { return Buffer.from(asc, 'base64').toString('binary'); }
            : atobPolyfill;
    //
    var _toUint8Array = _hasBuffer
        ? function (a) { return _U8Afrom(Buffer.from(a, 'base64')); }
        : function (a) { return _U8Afrom(_atob(a), function (c) { return c.charCodeAt(0); }); };
    /**
     * converts a Base64 string to a Uint8Array.
     */
    var toUint8Array = function (a) { return _toUint8Array(_unURI(a)); };
    //
    var _decode = _hasBuffer
        ? function (a) { return Buffer.from(a, 'base64').toString('utf8'); }
        : _TD
            ? function (a) { return _TD.decode(_toUint8Array(a)); }
            : function (a) { return btou(_atob(a)); };
    var _unURI = function (a) { return _tidyB64(a.replace(/[-_]/g, function (m0) { return m0 == '-' ? '+' : '/'; })); };
    /**
     * converts a Base64 string to a UTF-8 string.
     * @param {String} src Base64 string.  Both normal and URL-safe are supported
     * @returns {string} UTF-8 string
     */
    var decode = function (src) { return _decode(_unURI(src)); };
    /**
     * check if a value is a valid Base64 string
     * @param {String} src a value to check
      */
    var isValid = function (src) {
        if (typeof src !== 'string')
            return false;
        var s = src.replace(/\s+/g, '').replace(/={0,2}$/, '');
        return !/[^\s0-9a-zA-Z\+/]/.test(s) || !/[^\s0-9a-zA-Z\-_]/.test(s);
    };
    //
    var _noEnum = function (v) {
        return {
            value: v, enumerable: false, writable: true, configurable: true
        };
    };
    /**
     * extend String.prototype with relevant methods
     */
    var extendString = function () {
        var _add = function (name, body) { return Object.defineProperty(String.prototype, name, _noEnum(body)); };
        _add('fromBase64', function () { return decode(this); });
        _add('toBase64', function (urlsafe) { return encode(this, urlsafe); });
        _add('toBase64URI', function () { return encode(this, true); });
        _add('toBase64URL', function () { return encode(this, true); });
        _add('toUint8Array', function () { return toUint8Array(this); });
    };
    /**
     * extend Uint8Array.prototype with relevant methods
     */
    var extendUint8Array = function () {
        var _add = function (name, body) { return Object.defineProperty(Uint8Array.prototype, name, _noEnum(body)); };
        _add('toBase64', function (urlsafe) { return fromUint8Array(this, urlsafe); });
        _add('toBase64URI', function () { return fromUint8Array(this, true); });
        _add('toBase64URL', function () { return fromUint8Array(this, true); });
    };
    /**
     * extend Builtin prototypes with relevant methods
     */
    var extendBuiltins = function () {
        extendString();
        extendUint8Array();
    };
    var gBase64 = {
        version: version,
        VERSION: VERSION,
        atob: _atob,
        atobPolyfill: atobPolyfill,
        btoa: _btoa,
        btoaPolyfill: btoaPolyfill,
        fromBase64: decode,
        toBase64: encode,
        encode: encode,
        encodeURI: encodeURI,
        encodeURL: encodeURI,
        utob: utob,
        btou: btou,
        decode: decode,
        isValid: isValid,
        fromUint8Array: fromUint8Array,
        toUint8Array: toUint8Array,
        extendString: extendString,
        extendUint8Array: extendUint8Array,
        extendBuiltins: extendBuiltins
    };
    //
    // export Base64 to the namespace
    //
    // ES5 is yet to have Object.assign() that may make transpilers unhappy.
    // gBase64.Base64 = Object.assign({}, gBase64);
    gBase64.Base64 = {};
    Object.keys(gBase64).forEach(function (k) { return gBase64.Base64[k] = gBase64[k]; });
    return gBase64;
}));

}).call(this)}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {},require("buffer").Buffer)

},{"buffer":2}],5:[function(require,module,exports){
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const js_base64_1 = require("js-base64");
var defaultExcludeDomains = ["ogs.google.com", "meet.google.com", "play.google.com", "docs.google.com", "keep.google.com", "chrome.google.com", "support.mozilla.org",
    "codefileclient.danid.dk", "applet.danid.dk", "nemlog-in.mitid.dk", "business.comcast.com"];
var openrpadebug = false;
var openrpauniquexpathids = ['ng-model', 'ng-reflect-name']; // aria-label
var openrpautil = null;
if (openrpadebug)
    console.log("Hi from content");
function inIframe() {
    var result = true;
    try {
        if (window.self === window.top)
            return false;
        if (parent) {
        }
    }
    catch (e) {
    }
    return result;
}
function remotePushEvent(evt) {
    // if (openrpadebug) console.log("remotePushEvent, message received", evt);
    if (evt.data != null && evt.data.functionName == "mousemove") {
        var message = evt.data;
        if (typeof message === "string") {
            try {
                message = JSON.parse(message);
            }
            catch (e) { }
        }
        if (evt.data != null && evt.data.functionName == "mousemove") {
            openrpautil.parent = evt.data;
            try {
                notifyFrames(null);
            }
            catch (e) { }
        }
    }
}
if (window.addEventListener) {
    window.addEventListener("message", remotePushEvent, false);
}
else {
    window.attachEvent("onmessage", remotePushEvent);
}
var disallowPost = function () {
    if (document.URL.startsWith("https://docs.google.com/spreadsheets/d")) {
        return true;
    }
    if (document.URL.startsWith("https://codefileclient.danid.dk")) {
        return true;
    }
    if (document.URL.startsWith("https://applet.danid.dk")) {
        return true;
    }
    if (document.URL.startsWith("https://nemlog-in.mitid.dk")) {
        return true;
    }
    console.log(document.URL);
    return false;
};
var notifyFrames = (event) => {
    if (disallowPost())
        return;
    for (let targetElement of document.getElementsByTagName('iframe')) {
        var message = { functionName: 'mousemove', parents: 0, xpaths: [], uix: "", uiy: "", cssPath: "", xPath: "" };
        try {
            openrpautil.applyPhysicalCords(message, targetElement);
        }
        catch (e) {
            console.error(e);
        }
        if (openrpautil.parent != null) {
            message.parents = openrpautil.parent.parents + 1;
            message.uix += openrpautil.parent.uix;
            message.uiy += openrpautil.parent.uiy;
        }
        var width = getComputedStyle(targetElement, null).getPropertyValue('border-width');
        width = parseInt(width.replace('px', '')) * 0.85;
        message.uix += (width | 0);
        var height = getComputedStyle(targetElement, null).getPropertyValue('border-height');
        height = parseInt(height.replace('px', '')) * 0.85;
        message.uiy += (height | 0);
        message.cssPath = UTILS.cssPath(targetElement, false);
        message.xPath = UTILS.xPath(targetElement, true);
        // if (openrpadebug) console.log("post message to frame", message);
        targetElement.contentWindow.postMessage(JSON.stringify(message), '*');
    }
    var doFrames = () => {
        try {
            for (let targetElement of document.getElementsByTagName('frame')) {
                var message = { functionName: 'mousemove', parents: 0, xpaths: [], uix: "", uiy: "", cssPath: "", xPath: "" };
                try {
                    openrpautil.applyPhysicalCords(message, targetElement);
                }
                catch (e) {
                    console.error(e);
                }
                if (openrpautil.parent != null) {
                    message.parents = openrpautil.parent.parents + 1;
                    message.uix += openrpautil.parent.uix;
                    message.uiy += openrpautil.parent.uiy;
                }
                var width = getComputedStyle(targetElement, null).getPropertyValue('border-width');
                width = parseInt(width.replace('px', '')) * 0.85;
                message.uix += width;
                var height = getComputedStyle(targetElement, null).getPropertyValue('border-height');
                height = parseInt(height.replace('px', '')) * 0.85;
                message.uiy += (height | 0);
                message.cssPath = UTILS.cssPath(targetElement, false);
                message.xPath = UTILS.xPath(targetElement, true);
                targetElement.contentDocument.openrpautil.parent = message;
            }
        }
        catch (e) {
            setTimeout(doFrames, 500);
        }
    };
    doFrames();
};
if (!document.URL.startsWith("https://docs.google.com/spreadsheets/d")) {
    window.addEventListener('load', notifyFrames);
}
else {
    if (openrpadebug)
        console.warn("skip google docs");
}
var runtimeOnMessage = function (sender, message, fnResponse) {
    try {
        if ((sender && sender.openrpadebug) || openrpadebug) {
            if (openrpadebug)
                console.debug("sender", sender);
            if (openrpadebug)
                console.debug("message", message);
            if (openrpadebug)
                console.debug("fnResponse", fnResponse);
        }
        if (openrpautil == undefined)
            return;
        var func = openrpautil[sender.functionName];
        if (func) {
            var result = func(sender);
            if (result == null) {
                if (openrpadebug)
                    console.warn(sender.functionName + " gave no result.");
                fnResponse(sender);
            }
            else {
                fnResponse(result);
            }
        }
        else {
            sender.error = "Unknown function " + sender.functionName;
            fnResponse(sender);
        }
    }
    catch (e) {
        console.error('chrome.runtime.onMessage: error ');
        console.error(e);
        sender.error = e;
        fnResponse(sender);
    }
};
function doit() {
    try {
        // @ts-ignore
        chrome.extension.onMessage.addListener(runtimeOnMessage);
    }
    catch (error) {
    }
    try {
        chrome.runtime.onMessage.addListener(runtimeOnMessage);
    }
    catch (error) {
    }
    if (openrpadebug)
        console.debug('declaring openrpautil class 1');
    var cachecount = 0;
    openrpautil = {
        parent: null,
        ping: function () {
            return "pong";
        },
        init: function () {
            if (document.URL.startsWith("https://docs.google.com/spreadsheets/d")) {
                if (openrpadebug)
                    console.warn("skip google docs");
                return;
            }
            document.addEventListener('mousemove', function (e) { openrpautil.pushEvent('mousemove', e); }, true);
            if (inIframe()) {
                // if (openrpadebug) console.debug('in iframe, only register mouse move listener');
                return;
            }
            document.addEventListener('click', function (e) { openrpautil.pushEvent('click', e); }, true);
            document.addEventListener('keydown', function (e) { openrpautil.pushEvent('keydown', e); }, true);
            document.addEventListener('keypress', function (e) { openrpautil.pushEvent('keyup', e); }, true);
            document.addEventListener('mousedown', function (e) { openrpautil.pushEvent('mousedown', e); }, true);
        },
        findform: function (element) {
            try {
                var form = null;
                var ele = element;
                while (ele && !form) {
                    var name = ele.localName;
                    if (!name) {
                        ele = ele.parentNode;
                        continue;
                    }
                    name = name.toLowerCase();
                    if (name === "form")
                        form = ele;
                    ele = ele.parentNode;
                }
                return form;
            }
            catch (e) {
                console.error(e);
                return null;
            }
        },
        clickelement: function (message) {
            openrpadebug = message.debug;
            if (message.uniquexpathids)
                openrpauniquexpathids = message.uniquexpathids;
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
            }
            if (message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (xpathEle === null)
                    message.xPath = 'false';
                if (xpathEle !== null)
                    message.xPath = 'true';
                ele = xpathEle;
            }
            if (message.cssPath && ele === null) {
                var cssEle = document.querySelector(message.cssPath);
                if (cssEle === null)
                    message.cssPath = 'false';
                if (cssEle !== null)
                    message.cssPath = 'true';
                ele = cssEle;
            }
            try {
                if (ele !== null && ele !== undefined) {
                    var tagname = ele.tagName;
                    var tagtype = ele.getAttribute("type");
                    if (tagname)
                        tagname = tagname.toLowerCase();
                    if (tagtype)
                        tagtype = tagtype.toLowerCase();
                    if (tagname == "input" || tagtype == "type") {
                        var events = ["mousedown", "mouseup", "click", "submit"];
                        for (var i = 0; i < events.length; ++i) {
                            simulate(ele, events[i]);
                        }
                    }
                    else {
                        var events = ["mousedown", "mouseup", "click"];
                        for (var i = 0; i < events.length; ++i) {
                            simulate(ele, events[i]);
                        }
                    }
                }
            }
            catch (e) {
                console.error(e);
                message.error = e.message;
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug)
                console.log(test);
            return test;
        },
        focuselement: function (message) {
            openrpadebug = message.debug;
            if (message.uniquexpathids)
                openrpauniquexpathids = message.uniquexpathids;
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
            }
            if (message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (xpathEle === null)
                    message.xPath = 'false';
                if (xpathEle !== null)
                    message.xPath = 'true';
                ele = xpathEle;
            }
            if (message.cssPath && ele === null) {
                var cssEle = document.querySelector(message.cssPath);
                if (cssEle === null)
                    message.cssPath = 'false';
                if (cssEle !== null)
                    message.cssPath = 'true';
                ele = cssEle;
            }
            try {
                if (ele !== null && ele !== undefined) {
                    ele.scrollIntoView({ block: "center", behaviour: "smooth" });
                    var eventType = "onfocusin" in ele ? "focusin" : "focus", bubbles = "onfocusin" in ele, event;
                    if ("createEvent" in document) {
                        event = document.createEvent("Event");
                        event.initEvent(eventType, bubbles, true);
                    }
                    else if ("Event" in window) {
                        event = new Event(eventType, { bubbles: bubbles, cancelable: true });
                    }
                    ele.focus();
                    ele.dispatchEvent(event);
                    // openrpautil.getelement(message);
                }
            }
            catch (e) {
                console.error(e);
                message.error = e;
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug)
                console.log(test);
            return test;
        },
        getelements: function (message) {
            try {
                openrpadebug = message.debug;
                if (message.uniquexpathids)
                    openrpauniquexpathids = message.uniquexpathids;
                var fromele = null;
                if (message.fromxPath != null && message.fromxPath != "") {
                    if (message.fromxPath != null && message.fromxPath != "") {
                        if (openrpadebug)
                            console.log("fromele = document.evaluate('" + message.fromxPath + "', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;");
                        fromele = document.evaluate(message.fromxPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    }
                    else if (message.fromcssPath != null && message.fromcssPath != "") {
                        if (openrpadebug)
                            console.log("fromele = document.querySelector('" + message.fromcssPath + "');");
                        fromele = document.querySelector(message.fromcssPath);
                    }
                    if (fromele == null) {
                        var test = JSON.parse(JSON.stringify(message));
                        if (openrpadebug)
                            console.log("null hits when searching for anchor (from element!)");
                        return test;
                    }
                    if (openrpadebug)
                        console.log(fromele);
                }
                var ele = [];
                if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                    message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
                }
                if (ele.length === 0 && message.xPath) {
                    //var iterator = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                    var iterator;
                    var xpath = message.xPath;
                    var searchfrom = document;
                    if (fromele) {
                        var prexpath = UTILS.xPath(fromele, true);
                        // xpath = prexpath + message.xPath.substr(1, message.xPath.length - 1);
                        xpath = prexpath + message.xPath;
                        searchfrom = document;
                    }
                    iterator = document.evaluate(xpath, searchfrom, null, XPathResult.ANY_TYPE, null);
                    if (openrpadebug)
                        console.log("document.evaluate('" + xpath + "', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);");
                    try {
                        var thisNode = iterator.iterateNext();
                        if (thisNode && openrpadebug)
                            console.log(thisNode);
                        while (thisNode) {
                            ele.push(thisNode);
                            thisNode = iterator.iterateNext();
                        }
                    }
                    catch (e) {
                        console.error('Error: Document tree modified during iteration ' + e);
                    }
                    if (ele.length === 0)
                        message.xPath = 'false';
                    if (ele.length > 0)
                        message.xPath = 'true';
                }
                if (ele.length === 0 && message.cssPath) {
                    if (fromele == null) {
                        ele = document.querySelectorAll(message.cssPath);
                        if (openrpadebug)
                            console.log("document.querySelector('" + message.cssPath + "');");
                    }
                    else {
                        ele = fromele.querySelectorAll(message.cssPath);
                        if (openrpadebug)
                            console.log("fromele.querySelector('" + message.cssPath + "');");
                    }
                    if (ele.length === 0)
                        message.cssPath = 'false';
                    if (ele.length > 0)
                        message.cssPath = 'true';
                }
                var base = Object.assign({}, message);
                message.results = [];
                notifyFrames(null);
                if (ele.length > 0) {
                    try {
                        for (var i = 0; i < ele.length; i++) {
                            var result = Object.assign({}, base);
                            if (message.data === 'getdom') {
                                result.result = openrpautil.mapDOM(ele[i], false, true);
                            }
                            else {
                                result.result = openrpautil.mapDOM(ele[i], false);
                            }
                            try {
                                openrpautil.applyPhysicalCords(result, ele[i]);
                            }
                            catch (e) {
                                console.error(e);
                            }
                            result.zn_id = openrpautil.getuniqueid(ele[i]);
                            if (openrpautil.parent != null) {
                                result.parents = openrpautil.parent.parents + 1;
                                result.uix += openrpautil.parent.uix;
                                result.uiy += openrpautil.parent.uiy;
                                result.xpaths = openrpautil.parent.xpaths.slice(0);
                            }
                            else if (inIframe()) {
                                // TODO: exit?
                                //return;
                                var currentFramePosition = openrpautil.currentFrameAbsolutePosition();
                                result.uix += currentFramePosition.x;
                                result.uiy += currentFramePosition.y;
                            }
                            message.results.push(result);
                        }
                    }
                    catch (e) {
                        console.error(e);
                    }
                }
                else {
                }
            }
            catch (e) {
                console.error('error in getelements');
                message.error = e;
                console.error(e);
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug)
                console.log(test);
            return test;
        },
        getelement: function (message) {
            try {
                var ele = openrpautil.__getelement(message);
                if (ele !== null && ele !== undefined) {
                    try {
                        try {
                            openrpautil.applyPhysicalCords(message, ele);
                        }
                        catch (e) {
                            console.error(e);
                        }
                    }
                    catch (e) {
                        console.error(e);
                    }
                }
                if (ele !== null) {
                    if (message.data === 'getdom') {
                        message.result = openrpautil.mapDOM(ele, true, true, false);
                    }
                    else if (message.data === 'innerhtml') {
                        message.result = openrpautil.mapDOM(ele, true, true, true);
                    }
                    else {
                        message.result = openrpautil.mapDOM(ele, true);
                    }
                    message.zn_id = openrpautil.getuniqueid(ele);
                    if (openrpautil.parent != null) {
                        message.parents = openrpautil.parent.parents + 1;
                        message.uix += openrpautil.parent.uix;
                        message.uiy += openrpautil.parent.uiy;
                        message.xpaths = openrpautil.parent.xpaths.slice(0);
                    }
                    else if (inIframe()) {
                        // TODO: exit?
                        //return;
                        var currentFramePosition = openrpautil.currentFrameAbsolutePosition();
                        message.uix += currentFramePosition.x;
                        message.uiy += currentFramePosition.y;
                    }
                }
            }
            catch (e) {
                console.error('error in getelement');
                message.error = e;
                console.error(e);
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug)
                console.log(test);
            return test;
        },
        __getelement: function (message) {
            openrpadebug = message.debug;
            if (message.uniquexpathids)
                openrpauniquexpathids = message.uniquexpathids;
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
            }
            if (ele === null && message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (openrpadebug)
                    console.log("document.evaluate('" + message.xPath + "', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;");
                if (xpathEle === null)
                    message.xPath = 'false';
                if (xpathEle !== null)
                    message.xPath = 'true';
                ele = xpathEle;
            }
            if (ele === null && message.cssPath) {
                var cssEle = document.querySelector(message.cssPath);
                if (openrpadebug)
                    console.log("document.querySelector('" + message.cssPath + "');");
                if (cssEle === null)
                    message.cssPath = 'false';
                if (cssEle !== null)
                    message.cssPath = 'true';
                ele = cssEle;
            }
            return ele;
        },
        updateelementvalue: function (message) {
            if (openrpadebug)
                console.log("before", message);
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
                var znEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (znEle === null)
                    message.xPath = 'false';
                if (znEle !== null)
                    message.xPath = 'true';
                ele = znEle;
            }
            if (ele === null && message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (xpathEle === null)
                    message.xPath = 'false';
                if (xpathEle !== null)
                    message.xPath = 'true';
                ele = xpathEle;
            }
            if (ele === null && message.cssPath) {
                var cssEle = document.querySelector(message.cssPath);
                if (cssEle === null)
                    message.cssPath = 'false';
                if (cssEle !== null)
                    message.cssPath = 'true';
                ele = cssEle;
            }
            if (ele) {
                var data = message.data;
                try {
                    data = js_base64_1.Base64.decode(data);
                }
                catch (e) {
                    console.error(e);
                    console.log(data);
                }
                if (openrpadebug)
                    console.log('focus', ele);
                ele.focus();
                if (ele.tagName == "INPUT" && ele.getAttribute("type") == "checkbox") {
                    if (data === true || data === "true" || data === "True") {
                        if (openrpadebug)
                            console.log('set checked = true');
                        ele.checked = true;
                    }
                    else {
                        if (openrpadebug)
                            console.log('set checked = false');
                        ele.checked = false;
                    }
                }
                else if (message.result == "innerhtml") {
                    if (openrpadebug)
                        console.log('set value', data);
                    ele.innerHTML = data;
                }
                else if (message.result == "textcontent") {
                    if (openrpadebug)
                        console.log('set value', data);
                    ele.innerText = data;
                }
                else if (ele.tagName == "DIV") {
                    if (openrpadebug)
                        console.log('set value', data);
                    ele.innerText = data;
                }
                else {
                    if (openrpadebug)
                        console.log('set value', data);
                    ele.value = data;
                }
                try {
                    var evt = document.createEvent("HTMLEvents");
                    evt.initEvent("change", true, true);
                    ele.dispatchEvent(evt);
                }
                catch (e) {
                    console.error(e);
                }
                try {
                    var evt = document.createEvent("HTMLEvents");
                    evt.initEvent("input", true, true);
                    ele.dispatchEvent(evt);
                }
                catch (e) {
                    console.error(e);
                }
            }
            delete message.result;
            delete message.results;
            if (openrpadebug)
                console.log("after", message);
            var test = JSON.parse(JSON.stringify(message));
            return test;
        },
        updateelementvalues: function (message) {
            if (openrpadebug)
                console.log("before", message);
            var ele = null;
            if (ele === null && message.zn_id !== null && message.zn_id !== undefined && message.zn_id > -1) {
                message.xPath = '//*[@zn_id="' + message.zn_id + '"]';
                var znEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (znEle === null)
                    message.xPath = 'false';
                if (znEle !== null)
                    message.xPath = 'true';
                ele = znEle;
            }
            if (ele === null && message.xPath) {
                var xpathEle = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (xpathEle === null)
                    message.xPath = 'false';
                if (xpathEle !== null)
                    message.xPath = 'true';
                ele = xpathEle;
            }
            if (ele === null && message.cssPath) {
                var cssEle = document.querySelector(message.cssPath);
                if (cssEle === null)
                    message.cssPath = 'false';
                if (cssEle !== null)
                    message.cssPath = 'true';
                ele = cssEle;
            }
            if (ele) {
                var data = message.data;
                try {
                    data = js_base64_1.Base64.decode(data);
                }
                catch (e) {
                    console.error(e);
                    console.log(data);
                }
                ele.focus();
                var values = JSON.parse(data);
                if (ele.tagName && ele.tagName.toLowerCase() == "select") {
                    for (let i = 0; i < ele.options.length; i++) {
                        if (values.indexOf(ele.options[i].value) > -1) {
                            ele.options[i].selected = true;
                        }
                        else {
                            ele.options[i].selected = false;
                        }
                    }
                }
                try {
                    var evt = document.createEvent("HTMLEvents");
                    evt.initEvent("change", true, true);
                    ele.dispatchEvent(evt);
                }
                catch (e) {
                    console.error(e);
                }
                try {
                    var evt = document.createEvent("HTMLEvents");
                    evt.initEvent("input", true, true);
                    ele.dispatchEvent(evt);
                }
                catch (e) {
                    console.error(e);
                }
            }
            if (openrpadebug)
                console.log("after", message);
            delete message.result;
            delete message.results;
            var test = JSON.parse(JSON.stringify(message));
            return test;
        },
        applyPhysicalCords: function (message, ele) {
            try {
                var ClientRect = ele.getBoundingClientRect();
                var devicePixelRatio = window.devicePixelRatio || 1;
                var t = ele;
                var scrollLeft = (((t = document.documentElement) || (t = document.body.parentNode)) && typeof t.scrollLeft === 'number' ? t : document.body).scrollLeft;
                message.x = Math.floor(ClientRect.left);
                message.y = Math.floor(ClientRect.top);
                message.width = Math.floor(ele.offsetWidth);
                message.height = Math.floor(ele.offsetHeight);
                message.uiwidth = Math.round(ele.offsetWidth * devicePixelRatio);
                message.uiheight = Math.round(ele.offsetHeight * devicePixelRatio);
                if (window.self === window.top) {
                    message.uix = Math.round((ClientRect.left - scrollLeft) * devicePixelRatio);
                    message.uiy = Math.round((ClientRect.top * devicePixelRatio) + (window.outerHeight - (window.innerHeight * devicePixelRatio)));
                }
                else {
                    message.uix = Math.round(ClientRect.left * devicePixelRatio);
                    message.uiy = Math.round(ClientRect.top * devicePixelRatio);
                }
                if (inIframe() == false) {
                    var isAtMaxWidth = screen.availWidth - window.innerWidth === 0;
                    if (isAtMaxWidth) {
                        var isFirefox = typeof InstallTrigger !== 'undefined';
                        if (isFirefox) {
                            message.uix += 8;
                            message.uiy -= 7;
                        }
                        else {
                            message.uix += 8;
                            message.uiy += 8;
                        }
                    }
                    else {
                        message.uix += 7;
                        message.uiy -= 7;
                    }
                }
            }
            catch (error) {
                console.debug(error);
            }
        },
        // https://stackoverflow.com/questions/53056796/getboundingclientrect-from-within-iframe
        currentFrameAbsolutePosition: function () {
            let currentWindow = window;
            let currentParentWindow;
            let positions = [];
            let rect;
            if (inIframe()) {
            }
            currentParentWindow = parent;
            while (currentWindow !== window.top) {
                for (let idx = 0; idx < currentParentWindow.frames.length; idx++)
                    if (currentParentWindow.frames[idx] === currentWindow) {
                        // for (let frameElement of currentParentWindow.document.getElementsByTagName('iframe')) {
                        for (let t = 0; t < currentParentWindow.frames.length; t++) {
                            try {
                                let frameElement = currentParentWindow.frames[t];
                                if (typeof frameElement.getBoundingClientRect === "function") {
                                    rect = frameElement.getBoundingClientRect();
                                    positions.push({ x: rect.x, y: rect.y });
                                }
                                else if (frameElement.frameElement != null && typeof frameElement.frameElement.getBoundingClientRect === "function") {
                                    rect = frameElement.frameElement.getBoundingClientRect();
                                    positions.push({ x: rect.x, y: rect.y });
                                }
                                else if (frameElement.window != null && typeof frameElement.window.getBoundingClientRect === "function") {
                                    rect = frameElement.window.getBoundingClientRect();
                                    positions.push({ x: rect.x, y: rect.y });
                                }
                                else if (frameElement.contentWindow === currentWindow) {
                                    rect = frameElement.getBoundingClientRect();
                                    positions.push({ x: rect.x, y: rect.y });
                                }
                                else if (frameElement.window === currentWindow) {
                                    if (typeof frameElement.getBoundingClientRect === "function") {
                                        rect = frameElement.getBoundingClientRect();
                                        positions.push(rect);
                                    }
                                    else if (frameElement.frameElement != null && typeof frameElement.frameElement.getBoundingClientRect === "function") {
                                        rect = frameElement.frameElement.getBoundingClientRect();
                                        positions.push(rect);
                                    }
                                    else {
                                        positions.push({ x: 0, y: 0 });
                                    }
                                }
                            }
                            catch (e) {
                                // console.debug(e);
                                // console.error(e);
                                break;
                            }
                        }
                        //for (let frameElement of currentParentWindow.frames) {
                        //}
                        currentWindow = currentParentWindow;
                        currentParentWindow = currentWindow.parent;
                        break;
                    }
            }
            var result = positions.reduce((accumulator, currentValue) => {
                return {
                    x: (accumulator.x + currentValue.x) | 0,
                    y: (accumulator.y + currentValue.y) | 0
                };
            }, { x: 0, y: 0 });
            return result;
        },
        getOffset: function (el) {
            var _x = 0;
            var _y = 0;
            while (el && !isNaN(el.offsetLeft) && !isNaN(el.offsetTop)) {
                _x += el.offsetLeft - el.scrollLeft;
                _y += el.offsetTop - el.scrollTop;
                el = el.offsetParent;
            }
            return { top: _y, left: _x };
        },
        pushEvent: function (action, event) {
            let frame = -1;
            if (window.frameElement)
                frame = window.frameElement.id;
            if (action === 'keydown') {
                var evt = { functionName: action, key: String.fromCharCode(event.which) };
                // if (openrpadebug) console.log("sendMessage", evt);
                chrome.runtime.sendMessage(evt);
            }
            else if (action === 'keyup') {
                var evt = { functionName: action, key: String.fromCharCode(event.which) };
                // if (openrpadebug) console.log("sendMessage", evt);
                chrome.runtime.sendMessage(evt);
            }
            else {
                // https://www.jeffersonscher.com/res/resolution.php
                // https://stackoverflow.com/questions/3437786/get-the-size-of-the-screen-current-web-page-and-browser-window
                var message = { functionName: action, frame: frame, parents: 0, xpaths: [], uix: "", uiy: "", cssPath: "", xPath: "", zn_id: "", c: "", result: "" };
                var targetElement = null;
                targetElement = event.target || event.srcElement;
                if (targetElement == null) {
                    if (openrpadebug)
                        console.log('targetElement == null');
                    return;
                }
                try {
                    openrpautil.applyPhysicalCords(message, targetElement);
                }
                catch (e) {
                    console.error(e);
                }
                if (openrpautil.parent != null) {
                    message.parents = openrpautil.parent.parents + 1;
                    message.uix += openrpautil.parent.uix;
                    message.uiy += openrpautil.parent.uiy;
                    message.xpaths = openrpautil.parent.xpaths.slice(0);
                    //message.x += parent.uix;
                    //message.y += parent.uiy;
                    //message.width += parent.width;
                    //message.height += parent.height;
                }
                else if (inIframe()) {
                    // TODO: exit?
                    //return;
                    var currentFramePosition = openrpautil.currentFrameAbsolutePosition();
                    message.uix += currentFramePosition.x;
                    message.uiy += currentFramePosition.y;
                }
                message.cssPath = UTILS.cssPath(targetElement, false);
                message.xPath = UTILS.xPath(targetElement, true);
                message.zn_id = openrpautil.getuniqueid(targetElement);
                message.c = targetElement.childNodes.length;
                message.result = openrpautil.mapDOM(targetElement, true);
                message.xpaths.push(message.xPath);
                if (targetElement.contentWindow) {
                    if (disallowPost())
                        return;
                    var iframeWin = targetElement.contentWindow;
                    if (openrpadebug)
                        console.log("Post message to iframe", message);
                    iframeWin.postMessage(message, '*');
                    if (openrpadebug)
                        console.log('targetElement.tagName == iframe or frame');
                    return;
                }
                try {
                    // if (openrpadebug) console.log("sendMessage", message);
                    chrome.runtime.sendMessage(message);
                }
                catch (e) {
                    console.error(e);
                }
            }
        },
        getuniqueid: function (element) {
            if (element === null || element === undefined)
                return null;
            if (element.attributes === null || element.attributes === undefined)
                return null;
            for (var r = 0; r < element.attributes.length; r++) {
                var name = element.attributes[r].nodeName;
                if (name === 'zn_id')
                    return element.attributes[r].nodeValue;
            }
            if (element === null || element === undefined)
                return null;
            if (element.attributes === null || element.attributes === undefined)
                return null;
            ++cachecount;
            element.setAttribute('zn_id', cachecount);
            return cachecount;
        },
        executescript: function (message) {
            // try {
            //     openrpadebug = message.debug;
            //     if (openrpadebug) console.log(message);
            //     if (openrpadebug) console.log('script', message.script);
            //     message.result = eval(message.script);
            //     if (openrpadebug) console.log('result', message.result);
            // } catch (e) {
            //     console.error(e);
            //     message.error = e;
            // }
            // delete message.script;
            // var test = JSON.parse(JSON.stringify(message));
            // if (openrpadebug) console.log(test);
            // return test;
            try {
                if (document)
                    console.log('script', message.script);
                // message.result = eval(message.script);
                var s = document.createElement('script');
                s.async = false;
                s.src = message.script;
                s.addEventListener('load', function () {
                    // @ts-ignore
                    document.dynjsloaded = true;
                });
                document.body.appendChild(s);
                if (openrpadebug)
                    console.log('result', message.result);
            }
            catch (e) {
                console.error(e);
                message.error = e;
            }
            delete message.script;
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug)
                console.log(test);
            return test;
        },
        fullPath: function (el) {
            var names = [];
            while (el.parentNode) {
                if (el.id) {
                    names.unshift('#' + el.id);
                    break;
                }
                else {
                    if (el === el.ownerDocument.documentElement)
                        names.unshift(el.tagName);
                    else {
                        for (var c = 1, e = el; e.previousElementSibling; e = e.previousElementSibling, c++)
                            ;
                        names.unshift(el.tagName + ":nth-child(" + c + ")");
                    }
                    el = el.parentNode;
                }
            }
            return names.join(" > ");
        },
        toJSON: function (node, maxiden, ident) {
            if (ident === null || ident === undefined)
                ident = 0;
            if (maxiden === null || maxiden === undefined)
                ident = 1;
            node = node || this;
            var obj = {
                nodeType: node.nodeType, tagName: "", nodeName: "", nodeValue: undefined, attributes: [], childNodes: []
            };
            if (node.tagName) {
                obj.tagName = node.tagName.toLowerCase();
            }
            else if (node.nodeName) {
                obj.nodeName = node.nodeName;
            }
            if (node.nodeValue) {
                obj.nodeValue = node.nodeValue;
            }
            var attrs = node.attributes;
            if (attrs) {
                var length = attrs.length;
                var arr = obj.attributes = new Array(length);
                for (var i = 0; i < length; i++) {
                    let attr = attrs[i];
                    arr[i] = [attr.nodeName, attr.nodeValue];
                }
            }
            var childNodes = node.childNodes;
            if (childNodes && ident < maxiden) {
                length = childNodes.length;
                arr = obj.childNodes = new Array(length);
                for (i = 0; i < length; i++) {
                    arr[i] = openrpautil.toJSON(childNodes[i], maxiden, ident + 1);
                }
            }
            return obj;
        },
        toDOM: function (obj) {
            if (typeof obj === 'string') {
                obj = JSON.parse(obj);
            }
            var node, nodeType = obj.nodeType;
            switch (nodeType) {
                case 1: //ELEMENT_NODE
                    node = document.createElement(obj.tagName);
                    var attributes = obj.attributes || [];
                    for (var i = 0, len = attributes.length; i < len; i++) {
                        var attr = attributes[i];
                        node.setAttribute(attr[0], attr[1]);
                    }
                    break;
                case 3: //TEXT_NODE
                    node = document.createTextNode(obj.nodeValue);
                    break;
                case 8: //COMMENT_NODE
                    node = document.createComment(obj.nodeValue);
                    break;
                case 9: //DOCUMENT_NODE
                    node = document.implementation.createDocument(null, null);
                    break;
                case 10: //DOCUMENT_TYPE_NODE
                    node = document.implementation.createDocumentType(obj.nodeName, null, null);
                    break;
                case 11: //DOCUMENT_FRAGMENT_NODE
                    node = document.createDocumentFragment();
                    break;
                default:
                    return node;
            }
            if (nodeType === 1 || nodeType === 11) {
                var childNodes = obj.childNodes || [];
                for (i = 0, len = childNodes.length; i < len; i++) {
                    node.appendChild(openrpautil.toDOM(childNodes[i]));
                }
            }
            return node;
        },
        mapDOM: function (element, json, mapdom, innerhtml) {
            var maxiden = 40;
            if (mapdom !== true)
                maxiden = 1;
            if (maxiden === null || maxiden === undefined)
                maxiden = 20;
            var treeObject = {};
            // If string convert to document Node
            if (typeof element === "string") {
                let docNode;
                if (window.DOMParser) {
                    let parser = new DOMParser();
                    docNode = parser.parseFromString(element, "text/xml");
                }
                element = docNode.firstChild;
            }
            //Recursively loop through DOM elements and assign properties to object
            function treeHTML(element, object, maxiden, ident) {
                if (ident === null || ident === undefined)
                    ident = 0;
                if (maxiden === null || maxiden === undefined)
                    maxiden = 1;
                openrpautil.getuniqueid(element);
                object["tagName"] = element.tagName;
                if (ident === 0) {
                    object["xPath"] = UTILS.xPath(element, true);
                    object["cssPath"] = UTILS.cssPath(element, false);
                    if (object["tagName"] !== 'STYLE' && object["tagName"] !== 'SCRIPT' && object["tagName"] !== 'HEAD' && object["tagName"] !== 'HTML') {
                        if (element.innerText !== undefined && element.innerText !== null && element.innerText !== '') {
                            object["innerText"] = element.innerText;
                        }
                    }
                }
                var nodeList = element.childNodes;
                if (nodeList) {
                    if (nodeList.length) {
                        object["content"] = [];
                        for (var i = 0; i < nodeList.length; i++) {
                            if (nodeList[i].nodeType === 3) {
                                if (mapdom !== true) {
                                    if (object["tagName"] !== 'STYLE' && object["tagName"] !== 'SCRIPT' && object["tagName"] !== 'HEAD') {
                                        object["content"].push(nodeList[i].nodeValue);
                                    }
                                }
                            }
                            else {
                                if (ident < maxiden) {
                                    object["content"].push({});
                                    treeHTML(nodeList[i], object["content"][object["content"].length - 1], maxiden, ident + 1);
                                }
                            }
                        }
                    }
                }
                if (element.attributes) {
                    if (element.attributes.length) {
                        // To read values of disabled objects, we need to undisable them
                        //if (element.disabled === true) {
                        //    console.log('removing disabled!!!!');
                        //    wasDisabled = true;
                        //    //element.disabled == false;
                        //    element.removeAttribute("disabled");
                        //}
                        var attributecount = 0;
                        if (element.attributes["zn_id"] == undefined || element.attributes["zn_id"] == null) {
                            var zn_id = openrpautil.getuniqueid(element);
                        }
                        object["zn_id"] = element.attributes["zn_id"].nodeValue;
                        for (var r = 0; r < element.attributes.length; r++) {
                            var name = element.attributes[r].nodeName;
                            var value = element.attributes[r].nodeValue;
                            // value, innertext
                            if (ident === 0) {
                                if (mapdom !== true || name.toLowerCase() === 'zn_id') {
                                    object[name] = value;
                                    ++attributecount;
                                }
                                //if (['zn_id', 'id', 'classname', 'name', 'tagname', 'href', 'src', 'alt', 'clientrects'].includes(name.toLowerCase())) {
                                //    //object["attributes"][name] = value;
                                //    object[name] = value;
                                //    ++attributecount;
                                //}
                            }
                            else if (ident > 0 && mapdom === true) {
                                if (name.toLowerCase() === 'zn_id') {
                                    //object["attributes"][name] = value;
                                    object[name] = value;
                                    ++attributecount;
                                }
                            }
                        }
                        //if (attributecount === 0) delete object["attributes"];
                        // if (wasDisabled === true) {
                        //     if (ident === 0) {
                        //         //element.disabled == true;
                        //         element.setAttribute("disabled", "true");
                        //     }
                        // }
                    }
                }
            }
            treeHTML(element, treeObject, maxiden);
            treeObject["value"] = element.value;
            treeObject["isvisible"] = openrpautil.isVisible(element);
            treeObject["display"] = openrpautil.display(element);
            treeObject["isvisibleonscreen"] = openrpautil.isVisibleOnScreen(element);
            treeObject["disabled"] = element.disabled;
            treeObject["innerText"] = element.innerText;
            if (innerhtml) {
                treeObject["innerhtml"] = element.innerHTML;
            }
            if (element.tagName == "INPUT" && element.getAttribute("type") == "checkbox") {
                treeObject["checked"] = element.checked;
            }
            if (element.tagName && element.tagName.toLowerCase() == "options") {
                treeObject["selected"] = element.selected;
            }
            if (element.tagName && element.tagName.toLowerCase() == "select") {
                var selectedvalues = [];
                for (let i = 0; i < element.options.length; i++) {
                    if (element.options[i].selected) {
                        selectedvalues.push(element.options[i].value);
                        treeObject["text"] = element.options[i].text;
                    }
                }
                treeObject["values"] = selectedvalues;
            }
            //updateelementtext
            if (treeObject["disabled"] === null || treeObject["disabled"] === undefined)
                treeObject["disabled"] = false;
            return json ? JSON.stringify(treeObject) : treeObject;
        },
        isVisibleOnScreen: function (elm) {
            var rect = elm.getBoundingClientRect();
            var viewHeight = Math.max(document.documentElement.clientHeight, window.innerHeight);
            return !(rect.bottom < 0 || rect.top - viewHeight >= 0);
        },
        isVisible: function (elm) {
            return elm.offsetWidth > 0 && elm.offsetHeight > 0;
        },
        display: function (elm) {
            return window.getComputedStyle(elm, null).getPropertyValue('display');
        },
        getFrameName: function (frame) {
            var frames = parent.frames, l = frames.length, name = null;
            for (var x = 0; x < l; x++) {
                if (frames[x] === frame) {
                    name = frames[x].name;
                }
            }
            return name;
        },
        screenInfo: function () {
            return {
                screen: {
                    // availTop: window.screen.availTop,
                    // availLeft: window.screen.availLeft,
                    availHeight: window.screen.availHeight,
                    availWidth: window.screen.availWidth,
                    colorDepth: window.screen.colorDepth,
                    height: window.screen.height,
                    // left: window.screen.left,
                    orientation: window.screen.orientation,
                    pixelDepth: window.screen.pixelDepth,
                    // top: window.screen.top,
                    width: window.screen.width
                },
                screenX: window.screenX,
                screenY: window.screenY,
                screenLeft: window.screenLeft,
                screenTop: window.screenTop
            };
        },
        getXPath(el) {
            let nodeElem = el;
            if (nodeElem.id && this.options.shortid) {
                return `//*[@id="${nodeElem.id}"]`;
            }
            const parts = [];
            while (nodeElem && nodeElem.nodeType === Node.ELEMENT_NODE) {
                let nbOfPreviousSiblings = 0;
                let hasNextSiblings = false;
                let sibling = nodeElem.previousSibling;
                while (sibling) {
                    if (sibling.nodeType !== Node.DOCUMENT_TYPE_NODE && sibling.nodeName === nodeElem.nodeName) {
                        nbOfPreviousSiblings++;
                    }
                    sibling = sibling.previousSibling;
                }
                sibling = nodeElem.nextSibling;
                while (sibling) {
                    if (sibling.nodeName === nodeElem.nodeName) {
                        hasNextSiblings = true;
                        break;
                    }
                    sibling = sibling.nextSibling;
                }
                const prefix = nodeElem.prefix ? nodeElem.prefix + ':' : '';
                const nth = nbOfPreviousSiblings || hasNextSiblings ? `[${nbOfPreviousSiblings + 1}]` : '';
                parts.push(prefix + nodeElem.localName + nth);
                nodeElem = nodeElem.parentNode;
            }
            return parts.length ? '/' + parts.reverse().join('/') : '';
        },
        dispatchKeyboardEvent(element, type, character, keyCode, charCode) {
            if (character == null)
                character = String.fromCharCode(charCode);
            if (charCode == null)
                charCode = character.charCodeAt(0);
            if (keyCode == null)
                keyCode = character.charCodeAt(0); // view: window,
            var event = new KeyboardEvent(type, { "bubbles": true, "cancelable": true, "key": character, "ctrlKey": false, "shiftKey": false, "altKey": false, "charCode": charCode, "keyCode": keyCode, });
            var doc = document.ownerDocument || document;
            if (element == null)
                element = doc;
            element.dispatchEvent(event);
        },
        dispatchInputEvent(element, type, inputType, data) {
            var event = new InputEvent(type, { inputType: inputType, data: data });
            // @ts-ignore
            event.simulated = true;
            var doc = document.ownerDocument || document;
            if (element == null)
                element = doc;
            element.dispatchEvent(event);
        },
        sendtext(message) {
            try {
                var data = message.data;
                try {
                    data = js_base64_1.Base64.decode(data);
                }
                catch (e) {
                    console.error(e);
                    console.log(data);
                }
                const element = openrpautil.__getelement(message);
                console.log(element);
                if (element == null) {
                    throw new Error('SendText, failed locating element');
                }
                let parsekeys = false;
                if (message.reset == true) {
                    element.value = '';
                    delete message.reset;
                }
                if (message.parsekeys == true) {
                    parsekeys = true;
                    delete message.parsekeys;
                }
                let specialkey = null;
                for (let i = 0; i < data.length; ++i) {
                    let character = data[i];
                    let keyCode = character.toUpperCase().charCodeAt(i);
                    let charCode = data.charCodeAt(i);
                    if (character == "{") {
                        specialkey = "";
                        continue;
                    }
                    else if (character != "}" && specialkey != null) {
                        specialkey += character;
                        continue;
                    }
                    else if (character == "}") {
                        character = undefined;
                        keyCode = 0;
                        charCode = 0;
                        if (openrpautil.keynames[specialkey] != null) {
                            keyCode = openrpautil.keynames[specialkey];
                        }
                        else {
                            switch (specialkey.toLowerCase()) {
                                case "left55":
                                    keyCode = 37;
                                    break;
                                case "up55":
                                    keyCode = 38;
                                    break;
                                case "right55":
                                    keyCode = 39;
                                    break;
                                case "down55":
                                    keyCode = 40;
                                    break;
                            }
                        }
                    }
                    console.log("sendtext: " + character + " " + keyCode + " " + charCode);
                    openrpautil.dispatchKeyboardEvent(element, 'keydown', character, keyCode, charCode);
                    openrpautil.dispatchKeyboardEvent(element, 'keypress', character, keyCode, charCode);
                    openrpautil.dispatchInputEvent(element, "beforeinput", "insertText", character);
                    if (specialkey == null)
                        element.value += character;
                    openrpautil.dispatchKeyboardEvent(element, 'keyup', character, keyCode, charCode);
                    specialkey == null;
                }
            }
            catch (e) {
                console.error('error in getelements');
                message.error = e;
                console.error(e);
            }
            var test = JSON.parse(JSON.stringify(message));
            if (openrpadebug)
                console.log(test);
            return test;
        },
        settext(message) {
            message.reset = true;
            return openrpautil.sendtext(message);
        },
        sendkeys(message) {
            message.parsekeys = true;
            return openrpautil.sendtext(message);
        },
        keynames: {
            'break': 3,
            'backspace / delete': 8,
            'tab': 9,
            'clear': 12,
            'enter': 13,
            'shift': 16,
            'ctrl': 17,
            'alt': 18,
            'pause/break': 19,
            'caps lock': 20,
            'hangul': 21,
            'hanja': 25,
            'escape': 27,
            'conversion': 28,
            'non-conversion': 29,
            'spacebar': 32,
            'page up': 33,
            'page down': 34,
            'end': 35,
            'leftarrow': 37,
            'left': 37,
            'uparrow': 38,
            'up': 38,
            'rightarrow': 39,
            'right': 39,
            'downarrow': 40,
            'down': 40,
            'select': 41,
            'print': 42,
            'execute': 43,
            'printscreen': 44,
            'prtsrc': 44,
            'insert': 45,
            'delete': 46,
            'help': 47,
            '0': 48,
            '1': 49,
            '2': 50,
            '3': 51,
            '4': 52,
            '5': 53,
            '6': 54,
            '7': 55,
            '8': 56,
            '9': 57,
            ':': 58,
            'ffsemicolon': 59,
            'equals': 59,
            '<': 60,
            'ffequals': 61,
            'ß': 63,
            'ff@': 64,
            'a': 65,
            'b': 66,
            'c': 67,
            'd': 68,
            'e': 69,
            'f': 70,
            'g': 71,
            'h': 72,
            'i': 73,
            'j': 74,
            'k': 75,
            'l': 76,
            'm': 77,
            'n': 78,
            'o': 79,
            'p': 80,
            'q': 81,
            'r': 82,
            's': 83,
            't': 84,
            'u': 85,
            'v': 86,
            'w': 87,
            'x': 88,
            'y': 89,
            'z': 90,
            'windows': 91,
            'windowskey': 91,
            'leftwindows': 91,
            'leftwindowskey': 91,
            '?': 91,
            'searchkey': 91,
            'righttwindows': 92,
            'rightwindowskey': 92,
            'windowsmenu': 93,
            'sleep': 95,
            'numpad 0': 96,
            'numpad 1': 97,
            'numpad 2': 98,
            'numpad 3': 99,
            'numpad 4': 100,
            'numpad 5': 101,
            'numpad 6': 102,
            'numpad 7': 103,
            'numpad 8': 104,
            'numpad 9': 105,
            'multiply': 106,
            'add': 107,
            'numpad period (firefox)': 108,
            'subtract': 109,
            'decimal point': 110,
            'divide': 111,
            'f1': 112,
            'f2': 113,
            'f3': 114,
            'f4': 115,
            'f5': 116,
            'f6': 117,
            'f7': 118,
            'f8': 119,
            'f9': 120,
            'f10': 121,
            'f11': 122,
            'f12': 123,
            'f13': 124,
            'f14': 125,
            'f15': 126,
            'f16': 127,
            'f17': 128,
            'f18': 129,
            'f19': 130,
            'f20': 131,
            'f21': 132,
            'f22': 133,
            'f23': 134,
            'f24': 135,
            'f25': 136,
            'f26': 137,
            'f27': 138,
            'f28': 139,
            'f29': 140,
            'f30': 141,
            'f31': 142,
            'f32': 143,
            'numlock': 144,
            'scroll lock': 145,
            'airplane mode': 151,
            '^': 160,
            '!': 161,
            '#': 163,
            '$': 164,
            'ù': 165,
            'pagebackward': 166,
            'pageforward': 167,
            'refresh': 168,
            'closingparen': 169,
            '*': 170,
            '~': 171,
            'homekey': 172,
            'home': 172,
            'ffminus': 173,
            'minus': 173,
            'mute': 173,
            'unmute': 173,
            'decrease volume level': 174,
            'increase volume level': 175,
            'next': 176,
            'previous': 177,
            'stop': 178,
            'play/pause': 179,
            'email': 180,
            'mute/unmute (firefox)': 181,
            'decrease volume level (firefox)': 182,
            'increase volume level (firefox)': 183,
            'semi-colon / ñ': 186,
            'equal sign': 187,
            'comma': 188,
            'dash': 189,
            'period': 190,
            'forward slash / ç': 191,
            'grave accent / ñ / æ / ö': 192,
            '?, / or °': 193,
            'numpad period (chrome)': 194,
            'open bracket': 219,
            'back slash': 220,
            'close bracket / å': 221,
            'single quote / ø / ä': 222,
            '`': 223,
            'left or right ? key (firefox)': 224,
            'altgr': 225,
            '< /git >, left back slash': 226,
            'GNOME Compose Key': 230,
            'ç': 231,
            'XF86Forward': 233,
            'XF86Back': 234,
            'alphanumeric': 240,
            'hiragana/katakana': 242,
            'half-width/full-width': 243,
            'kanji': 244,
            'unlock trackpad (Chrome/Edge)': 251,
            'toggle touchpad': 255
        },
        gettablev1(message) {
            let data = message.data;
            // if data is string, parse it to json
            if (typeof data === 'string') {
                data = JSON.parse(data);
            }
            if (openrpadebug)
                console.debug('gettablev1', data);
            const domTabe = document.evaluate(message.xPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
            if (domTabe == null) {
                console.error('Failed locating table', message.xPath);
                const test = JSON.parse(JSON.stringify(message));
                return test;
            }
            const GetFirst = (element, xpath, prop) => {
                let value = null;
                const node = document.evaluate(xpath, element, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (node != null) {
                    value = node[prop];
                    if (value == null || value == '')
                        value = '';
                    value = value.split('\r').join('').split('\t').join('').split('\n').join('').trim();
                }
                return value;
            };
            const GetFirstText = (element, xpath) => {
                return GetFirst(element, xpath, 'textContent');
            };
            let rowsxpath = data.rowsxpath && data.rowsxpath != '' ? data.rowsxpath : '';
            let cellsxpath = data.cellsxpath && data.cellsxpath != '' ? data.cellsxpath : '';
            let cellxpath = data.cellxpath && data.cellxpath != '' ? data.cellxpath : '';
            let headerrowsxpath = data.headerrowsxpath && data.headerrowsxpath != '' ? data.headerrowsxpath : '';
            let headerrowxpath = data.headerrowxpath && data.headerrowxpath != '' ? data.headerrowxpath : '';
            let headerrowindex = data.headerrowindex ? data.headerrowindex : 0;
            let skiptypecheck = data.skiptypecheck != null && data.skiptypecheck != '' ? data.skiptypecheck : false;
            let isGoogleSearch = false;
            if (domTabe.nodeName.toLowerCase() == 'table') {
                rowsxpath = rowsxpath != '' ? rowsxpath : '//tr';
                cellsxpath = cellsxpath != '' ? cellsxpath : `//*[local-name()='td' or local-name()='th']`;
                cellxpath = cellxpath != '' ? cellxpath : '';
                headerrowsxpath = headerrowsxpath != '' ? headerrowsxpath : cellsxpath;
                headerrowxpath = headerrowxpath != '' ? headerrowxpath : '';
            }
            else if (domTabe.nodeName.toLowerCase() == 'div') {
                // @ts-ignore
                if (domTabe.id == 'rso') {
                    isGoogleSearch = true;
                    headerrowindex = -1;
                    rowsxpath = `//div[contains(concat(' ', normalize-space(@class), ' '), ' g ')]`;
                    // rowsxpath = `/div`;
                }
                else {
                    if (rowsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'row')]`) != null) {
                        rowsxpath = `//div[contains(@class, 'row')]`;
                    }
                    else if (rowsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'tableRow')]`) != null) {
                        rowsxpath = `//div[contains(@class, 'tableRow')]`;
                    }
                    else if (rowsxpath == '' && GetFirstText(domTabe, `//div[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'row')]`) != null) {
                        rowsxpath = `//div[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'row')]`;
                    }
                    else if (rowsxpath == '') {
                        console.error('Could not autodetect row class', domTabe.nodeName);
                        const test = JSON.parse(JSON.stringify(message));
                        return test;
                    }
                    console.log('rowsxpath', rowsxpath);
                    if (cellsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'col')]`) != null) {
                        cellsxpath = `//div[contains(@class, 'col')]`;
                    }
                    else if (cellsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'cell')]`) != null) {
                        cellsxpath = `//div[contains(@class, 'tableCell')]`;
                    }
                    else if (cellsxpath == '' && GetFirstText(domTabe, `.//div[contains(@class, 'tableCell')]`) != null) {
                        cellsxpath = `//div[contains(@class, 'tableCell')]`;
                    }
                    else if (cellsxpath == '' && GetFirstText(domTabe, `.//*[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'col')`) != null) {
                        cellsxpath = `//*[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'col')`;
                    }
                    else if (cellsxpath == '' && GetFirstText(domTabe, `.//*[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'cell')`) != null) {
                        cellsxpath = `//*[contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'cell')`;
                    }
                    else {
                        console.error('Could not autodetect column class', domTabe.nodeName);
                        const test = JSON.parse(JSON.stringify(message));
                        return test;
                    }
                    console.log('cellsxpath', cellsxpath);
                }
                cellxpath = cellxpath != '' ? cellxpath : '';
                headerrowsxpath = headerrowsxpath != '' ? headerrowsxpath : cellsxpath;
                headerrowxpath = headerrowxpath != '' ? headerrowxpath : '';
            }
            else {
                console.error('Table is of unknown type', domTabe.nodeName);
                const test = JSON.parse(JSON.stringify(message));
                return test;
            }
            if (openrpadebug)
                console.debug('skiptypecheck', skiptypecheck);
            const headers = [];
            const table = [];
            const isFloat = (val) => {
                const floatRegex = /^-?\d+(?:[.,]\d*?)?$/;
                if (!floatRegex.test(val))
                    return false;
                const newval = parseFloat(val);
                if (isNaN(newval))
                    return false;
                return true;
            };
            const isInt = (val) => {
                const intRegex = /^-?\d+$/;
                if (!intRegex.test(val))
                    return false;
                const intVal = parseInt(val, 10);
                return parseFloat(val) == intVal && !isNaN(intVal);
            };
            if (openrpadebug)
                console.debug('Working with table', domTabe);
            const query = document.evaluate('.' + rowsxpath, domTabe, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
            if (openrpadebug)
                console.debug('found ' + query.snapshotLength + ' rows using ' + rowsxpath);
            if (isGoogleSearch) {
                headers.push(['Title']);
                headers.push(['URL']);
                headers.push(['Description']);
            }
            for (let i = 0; i < query.snapshotLength; i++) {
                const row = query.snapshotItem(i);
                let subquery = null;
                if (i == headerrowindex && !isGoogleSearch) {
                    if (openrpadebug)
                        console.debug('headers row', row);
                    if (!data.headerrowsxpath || data.headerrowsxpath == '') {
                        subquery = document.evaluate('.//th', row, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                        if (subquery.snapshotLength == 0) {
                            subquery = document.evaluate('.//td', row, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                        }
                    }
                    else {
                        subquery = document.evaluate('.' + headerrowsxpath, row, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                    }
                    if (openrpadebug)
                        console.debug('headers row found ' + subquery.snapshotLength + ' cells using ' + headerrowsxpath);
                    for (let y = 0; y < subquery.snapshotLength; y++) {
                        const cel = subquery.snapshotItem(y);
                        let _name = cel.textContent;
                        if (headerrowxpath != '') {
                            _name = '';
                            let __name = GetFirstText(cel, '.' + headerrowxpath);
                            if (__name != null && __name != '')
                                _name = __name;
                        }
                        else {
                            let __name = GetFirstText(cel, './span');
                            if (__name == null)
                                __name = GetFirstText(cel, './b');
                            if (__name == null)
                                __name = GetFirstText(cel, './strong');
                            if (__name == null)
                                __name = GetFirstText(cel, './em');
                            if (__name == null)
                                __name = GetFirstText(cel, './/span');
                            if (__name == null)
                                __name = GetFirstText(cel, './/b');
                            if (__name == null)
                                __name = GetFirstText(cel, './/strong');
                            if (__name == null)
                                __name = GetFirstText(cel, './/descendant::div[last()]');
                            if (__name == null)
                                __name = GetFirstText(cel, './/em');
                            if (__name != null && __name != '')
                                _name = __name;
                        }
                        if (_name == null || _name == '')
                            _name = '';
                        _name = _name.split('\r').join('').split('\t').join('').split('\n').join('').trim();
                        if (!_name || _name == '')
                            _name = 'cell' + (y + 1);
                        headers.push(_name);
                    }
                    if (openrpadebug)
                        console.debug('headers', headers);
                }
                if (i <= headerrowindex)
                    continue;
                subquery = document.evaluate('.' + cellsxpath, row, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                if (openrpadebug)
                    console.log('row', i, 'found ' + subquery.snapshotLength + ' cells using ' + cellsxpath);
                const obj = {};
                let hadvalue = false;
                if (isGoogleSearch) {
                    const title = GetFirstText(row, './/h3');
                    const url = GetFirst(row, './/a', 'href');
                    let description = GetFirstText(row, `.//span[contains(concat(' ', normalize-space(@class), ' '), ' st ')]`);
                    if (description == null)
                        description = GetFirstText(row, `.//span[contains(concat(' ', normalize-space(@class), ' '), ' f ')]`);
                    if (description == null)
                        description = GetFirstText(row, `.//div[@data-content-feature='1']`);
                    // if (description == null) description = GetFirstText(row, './/cite')
                    // //span[@class='f']/following-sibling::text()
                    obj['Title'] = title;
                    obj['URL'] = url;
                    obj['Description'] = description;
                    hadvalue = true;
                }
                else {
                    for (let y = 0; y < subquery.snapshotLength; y++) {
                        let cell = subquery.snapshotItem(y);
                        let val = cell.textContent;
                        if (cellxpath != '') {
                            val = '';
                            let __val = GetFirstText(cell, '.' + cellxpath);
                            if (__val != null && __val != '')
                                val = __val;
                        }
                        if (!val || val == '')
                            val = '';
                        while (val.endsWith('\n'))
                            val = val.substring(0, val.length - 1);
                        while (val.startsWith('\n'))
                            val = val.substring(1, val.length);
                        while (val.endsWith('\t'))
                            val = val.substring(0, val.length - 1);
                        while (val.startsWith('\t'))
                            val = val.substring(1, val.length);
                        val = val.trim();
                        if (!skiptypecheck) {
                            const input = document.evaluate(`.//input[@type='checkbox']`, cell, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                            if (input != null) {
                                // @ts-ignore
                                val = input.checked;
                            }
                            if (isFloat(val)) {
                                val = parseFloat(val);
                            }
                            else if (isInt(val)) {
                                val = Number.parseInt(val);
                                // is boolean 
                            }
                            else if (val == true || val == false) {
                                val = val;
                            }
                            else if (val && val.toLowerCase() == 'true') {
                                val = true;
                            }
                            else if (val && val.toLowerCase() == 'false') {
                                val = false;
                            }
                            else {
                                // xpath find input of type checkbox and then check if it is checked
                            }
                        }
                        let name = 'cell' + (y + 1);
                        if (headers.length > y) {
                            name = headers[y];
                        }
                        obj[name] = val;
                        if (val != '')
                            hadvalue = true;
                    }
                }
                if (hadvalue)
                    table.push(obj);
            }
            console.log(table);
            message.result = table;
            const test = JSON.parse(JSON.stringify(message));
            return test;
        } // end gettablev1
    };
    openrpautil.init();
}
if (typeof openrpautil === 'undefined' || openrpautil == null) {
    let url = document.URL;
    let ExcludeDomains = defaultExcludeDomains.join(",");
    chrome.storage.sync.get({ excludeDomains: defaultExcludeDomains.join(",") }, (items) => {
        ExcludeDomains = items.excludeDomains.split(",");
        for (let i = 0; i < ExcludeDomains.length; i++) {
            let part = ExcludeDomains[i].trim();
            if (url.startsWith("http://" + part) || url.startsWith("https://" + part) || url.startsWith("http://www." + part) || url.startsWith("https://www." + part)) {
                // console.debug("skip page", part);
                return;
            }
        }
        doit();
    });
    // https://chromium.googlesource.com/chromium/blink/+/master/Source/devtools/front_end/components/DOMPresentationUtils.js
    // https://gist.github.com/asfaltboy/8aea7435b888164e8563
    /*
     * Copyright (C) 2015 Pavel Savshenko
     * Copyright (C) 2011 Google Inc.  All rights reserved.
     * Copyright (C) 2007, 2008 Apple Inc.  All rights reserved.
     * Copyright (C) 2008 Matt Lilek <<EMAIL>>
     * Copyright (C) 2009 Joseph Pecoraro
     *
     * Redistribution and use in source and binary forms, with or without
     * modification, are permitted provided that the following conditions
     * are met:
     *
     * 1.  Redistributions of source code must retain the above copyright
     *     notice, this list of conditions and the following disclaimer.
     * 2.  Redistributions in binary form must reproduce the above copyright
     *     notice, this list of conditions and the following disclaimer in the
     *     documentation and/or other materials provided with the distribution.
     * 3.  Neither the name of Apple Computer, Inc. ("Apple") nor the names of
     *     its contributors may be used to endorse or promote products derived
     *     from this software without specific prior written permission.
     *
     * THIS SOFTWARE IS PROVIDED BY APPLE AND ITS CONTRIBUTORS "AS IS" AND ANY
     * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
     * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
     * DISCLAIMED. IN NO EVENT SHALL APPLE OR ITS CONTRIBUTORS BE LIABLE FOR ANY
     * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
     * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
     * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
     * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
     * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
     * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
     */
    var UTILS = {
        xPath: function (node, optimized) {
            if (node.nodeType === Node.DOCUMENT_NODE)
                return "/";
            var steps = [];
            var contextNode = node;
            while (contextNode) {
                var step = UTILS._xPathValue(contextNode, optimized);
                if (!step)
                    break; // Error - bail out early.
                steps.push(step);
                if (step.optimized)
                    break;
                contextNode = contextNode.parentNode;
            }
            steps.reverse();
            return (steps.length && steps[0].optimized ? "" : "/") + steps.join("/");
        },
        _xPathValue: function (node, optimized) {
            var ownValue;
            var ownIndex = UTILS._xPathIndex(node);
            if (ownIndex === -1)
                return null; // Error.
            switch (node.nodeType) {
                case Node.ELEMENT_NODE:
                    ownValue = node.localName;
                    if (optimized) {
                        for (var i = 0; i < openrpauniquexpathids.length; i++) {
                            var id = openrpauniquexpathids[i].toLowerCase();
                            if (node.getAttribute(id))
                                return new UTILS.DOMNodePathStep("//" + ownValue + "[@" + id + "=\"" + node.getAttribute(id) + "\"]", true);
                            id = id.toUpperCase();
                            if (node.getAttribute(id))
                                return new UTILS.DOMNodePathStep("//" + ownValue + "[@" + id + "=\"" + node.getAttribute(id) + "\"]", true);
                        }
                    }
                    if (optimized && node.getAttribute("id"))
                        return new UTILS.DOMNodePathStep("//" + ownValue + "[@id=\"" + node.getAttribute("id") + "\"]", true);
                    break;
                case Node.ATTRIBUTE_NODE:
                    ownValue = "@" + node.nodename;
                    break;
                case Node.TEXT_NODE:
                case Node.CDATA_SECTION_NODE:
                    ownValue = "text()";
                    break;
                case Node.PROCESSING_INSTRUCTION_NODE:
                    ownValue = "processing-instruction()";
                    break;
                case Node.COMMENT_NODE:
                    ownValue = "comment()";
                    break;
                case Node.DOCUMENT_NODE:
                    ownValue = "";
                    break;
                default:
                    ownValue = "";
                    break;
            }
            if (ownIndex > 0)
                ownValue += "[" + ownIndex + "]";
            return new UTILS.DOMNodePathStep(ownValue, node.nodeType === Node.DOCUMENT_NODE);
        },
        _xPathIndex: function (node) {
            // Returns -1 in case of error, 0 if no siblings matching the same expression, <XPath index among the same expression-matching sibling nodes> otherwise.
            function areNodesSimilar(left, right) {
                if (left === right)
                    return true;
                if (left.nodeType === Node.ELEMENT_NODE && right.nodeType === Node.ELEMENT_NODE)
                    return left.localName === right.localName;
                if (left.nodeType === right.nodeType)
                    return true;
                // XPath treats CDATA as text nodes.
                var leftType = left.nodeType === Node.CDATA_SECTION_NODE ? Node.TEXT_NODE : left.nodeType;
                var rightType = right.nodeType === Node.CDATA_SECTION_NODE ? Node.TEXT_NODE : right.nodeType;
                return leftType === rightType;
            }
            var siblings = node.parentNode ? node.parentNode.children : null;
            if (!siblings)
                return 0; // Root node - no siblings.
            var hasSameNamedElements;
            for (var i = 0; i < siblings.length; ++i) {
                if (areNodesSimilar(node, siblings[i]) && siblings[i] !== node) {
                    hasSameNamedElements = true;
                    break;
                }
            }
            if (!hasSameNamedElements)
                return 0;
            var ownIndex = 1; // XPath indices start with 1.
            for (var z = 0; z < siblings.length; ++z) {
                if (areNodesSimilar(node, siblings[z])) {
                    if (siblings[z] === node)
                        return ownIndex;
                    ++ownIndex;
                }
            }
            return -1; // An error occurred: |node| not found in parent's children.
        },
        cssPath: function (node, optimized) {
            if (node.nodeType !== Node.ELEMENT_NODE)
                return "";
            var steps = [];
            var contextNode = node;
            while (contextNode) {
                var step = UTILS._cssPathStep(contextNode, !!optimized, contextNode === node);
                if (!step)
                    break; // Error - bail out early.
                steps.push(step);
                if (step.optimized)
                    break;
                contextNode = contextNode.parentNode;
            }
            steps.reverse();
            return steps.join(" > ");
        },
        _cssPathStep: function (node, optimized, isTargetNode) {
            if (node.nodeType !== Node.ELEMENT_NODE)
                return null;
            var id = node.getAttribute("id");
            if (optimized) {
                if (id)
                    return new UTILS.DOMNodePathStep(idSelector(id), true);
                var nodeNameLower = node.nodeName.toLowerCase();
                if (nodeNameLower === "body" || nodeNameLower === "head" || nodeNameLower === "html")
                    return new UTILS.DOMNodePathStep(node.nodeName.toLowerCase(), true);
            }
            var nodeName = node.nodeName.toLowerCase();
            if (id && optimized)
                return new UTILS.DOMNodePathStep(nodeName.toLowerCase() + idSelector(id), true);
            var parent = node.parentNode;
            if (!parent || parent.nodeType === Node.DOCUMENT_NODE)
                return new UTILS.DOMNodePathStep(nodeName.toLowerCase(), true);
            function prefixedElementClassNames(node) {
                var classAttribute = node.getAttribute("class");
                if (!classAttribute)
                    return [];
                return classAttribute.split(/\s+/g).filter(Boolean).map(function (name) {
                    // The prefix is required to store "__proto__" in a object-based map.
                    return "$" + name;
                });
            }
            function idSelector(id) {
                return "#" + escapeIdentifierIfNeeded(id);
            }
            function escapeIdentifierIfNeeded(ident) {
                if (isCSSIdentifier(ident))
                    return ident;
                var shouldEscapeFirst = /^(?:[0-9]|-[0-9-]?)/.test(ident);
                var lastIndex = ident.length - 1;
                return ident.replace(/./g, function (c, i) {
                    return shouldEscapeFirst && i === 0 || !isCSSIdentChar(c) ? escapeAsciiChar(c, i === lastIndex) : c;
                });
            }
            function escapeAsciiChar(c, isLast) {
                return "\\" + toHexByte(c) + (isLast ? "" : " ");
            }
            function toHexByte(c) {
                var hexByte = c.charCodeAt(0).toString(16);
                if (hexByte.length === 1)
                    hexByte = "0" + hexByte;
                return hexByte;
            }
            function isCSSIdentChar(c) {
                if (/[a-zA-Z0-9_-]/.test(c))
                    return true;
                return c.charCodeAt(0) >= 0xA0;
            }
            function isCSSIdentifier(value) {
                return /^-?[a-zA-Z_][a-zA-Z0-9_-]*$/.test(value);
            }
            var prefixedOwnClassNamesArray = prefixedElementClassNames(node);
            var needsClassNames = false;
            var needsNthChild = false;
            var ownIndex = -1;
            var siblings = parent.children;
            for (var i = 0; (ownIndex === -1 || !needsNthChild) && i < siblings.length; ++i) {
                var sibling = siblings[i];
                if (sibling === node) {
                    ownIndex = i;
                    continue;
                }
                if (needsNthChild)
                    continue;
                if (sibling.nodeName.toLowerCase() !== nodeName.toLowerCase())
                    continue;
                needsClassNames = true;
                var ownClassNames = prefixedOwnClassNamesArray;
                var ownClassNameCount = 0;
                for (var name in ownClassNames)
                    ++ownClassNameCount;
                if (ownClassNameCount === 0) {
                    needsNthChild = true;
                    continue;
                }
                var siblingClassNamesArray = prefixedElementClassNames(sibling);
                for (var j = 0; j < siblingClassNamesArray.length; ++j) {
                    var siblingClass = siblingClassNamesArray[j];
                    if (ownClassNames.indexOf(siblingClass))
                        continue;
                    delete ownClassNames[siblingClass];
                    if (!--ownClassNameCount) {
                        needsNthChild = true;
                        break;
                    }
                }
            }
            var result = nodeName.toLowerCase();
            if (isTargetNode && nodeName.toLowerCase() === "input" && node.getAttribute("type") && !node.getAttribute("id") && !node.getAttribute("class"))
                result += "[type=\"" + node.getAttribute("type") + "\"]";
            if (needsNthChild) {
                result += ":nth-child(" + (ownIndex + 1) + ")";
            }
            else if (needsClassNames) {
                for (var prefixedName in prefixedOwnClassNamesArray)
                    // for (var prefixedName in prefixedOwnClassNamesArray.keySet())
                    result += "." + escapeIdentifierIfNeeded(prefixedOwnClassNamesArray[prefixedName].substr(1));
            }
            return new UTILS.DOMNodePathStep(result, false);
        },
        DOMNodePathStep: function (value, optimized) {
            this.value = value;
            this.optimized = optimized || false;
        }
    };
    UTILS.DOMNodePathStep.prototype = {
        toString: function () {
            return this.value;
        }
    };
}
function extend(destination, source) {
    for (var property in source)
        destination[property] = source[property];
    return destination;
}
var eventMatchers = {
    'HTMLEvents': /^(?:load|unload|abort|error|select|change|submit|reset|focus|blur|resize|scroll)$/,
    'MouseEvents': /^(?:click|dblclick|mouse(?:down|up|over|move|out))$/
};
var defaultOptions = {
    pointerX: 0,
    pointerY: 0,
    button: 0,
    ctrlKey: false,
    altKey: false,
    shiftKey: false,
    metaKey: false,
    bubbles: true,
    cancelable: true
};
function simulate(element, eventName) {
    var options = extend(defaultOptions, arguments[2] || {});
    var oEvent, eventType = null;
    for (var name in eventMatchers) {
        if (eventMatchers[name].test(eventName)) {
            eventType = name;
            break;
        }
    }
    if (!eventType)
        throw new SyntaxError('Only HTMLEvents and MouseEvents interfaces are supported');
    if (document.createEvent) {
        oEvent = document.createEvent(eventType);
        if (eventType == 'HTMLEvents') {
            oEvent.initEvent(eventName, options.bubbles, options.cancelable);
        }
        else {
            oEvent.initMouseEvent(eventName, options.bubbles, options.cancelable, document.defaultView, options.button, options.pointerX, options.pointerY, options.pointerX, options.pointerY, options.ctrlKey, options.altKey, options.shiftKey, options.metaKey, options.button, element);
        }
        element.dispatchEvent(oEvent);
    }
    else {
        options.clientX = options.pointerX;
        options.clientY = options.pointerY;
        var evt = document.createEventObject();
        oEvent = extend(evt, options);
        element.fireEvent('on' + eventName, oEvent);
    }
    return element;
}

},{"js-base64":4}]},{},[5])
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
