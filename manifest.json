{"action": {}, "background": {"service_worker": "background.js"}, "browser_specific_settings": {"gecko": {"id": "info@test"}}, "key": "U1haTl9ycGFfZXh0ZW50aW9u", "content_scripts": [{"all_frames": true, "js": ["content.js"], "match_about_blank": true, "matches": ["http://*/*", "https://*/*", "file://*/*"], "run_at": "document_start"}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "description": "Support extension for SXRPA", "icons": {"128": "images/SXRPA128.png", "16": "images/SXRPA16.png", "48": "images/SXRPA48.png"}, "manifest_version": 3, "name": "SXRPA", "options_page": "options.html", "permissions": ["nativeMessaging", "webNavigation", "downloads", "debugger", "scripting", "activeTab", "storage", "tabs"], "version": "1.2"}